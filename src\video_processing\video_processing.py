import os
import warnings
import subprocess
from typing import Final

from moviepy import (
    Audio<PERSON><PERSON>Clip,
    VideoFileClip,
    concatenate_videoclips,
    concatenate_audioclips,
)
from moviepy.video.VideoClip import VideoClip
from src.video_processing.ffmpeg import FFmpeg, HWAccel
from src.utils.logger import logger

_DEFAULT_FPS: Final[int] = 30
_DEFAULT_DUBBED_VIDEO_FILE: Final[str] = "dubbed_video"
_DEFAULT_OUTPUT_FORMAT: Final[str] = ".mp4"

_DEFAULT_INPUT_DIRECTORY: Final[str] = "data/inputs/"


class VideoProcessing:
    _GPU_ENCODER = "libx264"  # default to cpu encoder

    @staticmethod
    def _setup_gpu_acceleration():
        """configure moviepy to use system ffmpeg with hardware acceleration"""
        # get the system ffmpeg path
        try:
            ffmpeg_path = subprocess.check_output(
                ["which", "ffmpeg"], text=True
            ).strip()
        except (subprocess.SubprocessError, FileNotFoundError):
            try:
                # windows alternative
                ffmpeg_path = (
                    subprocess.check_output(["where", "ffmpeg"], text=True)
                    .strip()
                    .splitlines()[0]
                )
            except (subprocess.SubprocessError, FileNotFoundError, IndexError):
                # fallback to default
                return

        # set environment variable for imageio_ffmpeg to use system ffmpeg instead of bundled version
        if ffmpeg_path:
            os.environ["IMAGEIO_FFMPEG_EXE"] = ffmpeg_path

        # get recommended hardware acceleration for extra arguments
        hwaccel = FFmpeg.get_recommended_hwaccel()
        if hwaccel != HWAccel.none:
            # determine appropriate hardware encoder based on acceleration method
            hw_encoder = None
            if hwaccel in [HWAccel.cuda, HWAccel.nvenc]:
                hw_encoder = "h264_nvenc"
            elif hwaccel == HWAccel.qsv:
                hw_encoder = "h264_qsv"
            elif hwaccel == HWAccel.vaapi:
                hw_encoder = "h264_vaapi"
            elif hwaccel == HWAccel.amf:
                hw_encoder = "h264_amf"

            # configure global moviepy settings
            if hw_encoder:
                # update default codec settings
                VideoProcessing._GPU_ENCODER = hw_encoder
                logger().info(f"using hardware encoder: {hw_encoder}")

                # set ffmpeg environment variable
                os.environ["FFMPEG_BINARY"] = ffmpeg_path
                logger().info(
                    f"using system ffmpeg for moviepy operations: {ffmpeg_path}"
                )

                # patch videoclip's write_videofile method to use gpu acceleration by default
                VideoProcessing._patch_video_clip_write(hwaccel, hw_encoder)

                logger().info(
                    f"gpu acceleration enabled for all ffmpeg operations. expect significantly improved performance."
                )

    @staticmethod
    def _patch_video_clip_write(hwaccel, hw_encoder):
        """patch videoclip.write_videofile to use gpu acceleration by default"""
        try:
            # get the original method
            original_write_videofile = VideoClip.write_videofile

            # define the patched method
            def patched_write_videofile(self, filename, **kwargs):
                # use hardware encoder if codec not specified
                if "codec" not in kwargs or kwargs["codec"] is None:
                    kwargs["codec"] = hw_encoder

                # add hardware acceleration parameters
                if "ffmpeg_params" not in kwargs:
                    kwargs["ffmpeg_params"] = []

                # add hwaccel parameter based on available acceleration
                hwaccel_params = []
                if hwaccel == HWAccel.cuda:
                    hwaccel_params = ["-hwaccel", "cuda"]
                elif hwaccel == HWAccel.nvenc:
                    hwaccel_params = ["-hwaccel", "nvenc"]
                elif hwaccel == HWAccel.qsv:
                    hwaccel_params = ["-hwaccel", "qsv"]
                elif hwaccel == HWAccel.vaapi:
                    hwaccel_params = ["-hwaccel", "vaapi"]
                elif hwaccel == HWAccel.amf:
                    hwaccel_params = ["-hwaccel", "amf"]

                # add hwaccel to ffmpeg_params if not already there
                if hwaccel_params and all(
                    p not in kwargs["ffmpeg_params"] for p in hwaccel_params
                ):
                    kwargs["ffmpeg_params"].extend(hwaccel_params)

                # call the original method with our modifications
                return original_write_videofile(self, filename, **kwargs)

            # replace the original method with our patched version
            VideoClip.write_videofile = patched_write_videofile

            logger().info(
                f"successfully patched moviepy to use {hwaccel.value} acceleration"
            )

        except (ImportError, AttributeError) as e:
            logger().warning(f"failed to patch moviepy for gpu acceleration: {str(e)}")

    @staticmethod
    def split_audio_video(*, video_file: str, output_directory: str) -> tuple[str, str]:
        """splits an audio/video file into separate audio and video files with hardware acceleration and parallel processing."""
        import concurrent.futures
        import threading

        base_filename = os.path.basename(video_file)
        filename, ext = os.path.splitext(base_filename)

        # use hardware acceleration for ffmpeg operations
        recommended_hwaccel = FFmpeg.get_recommended_hwaccel()
        ffmpeg = FFmpeg(recommended_hwaccel)

        logger().debug(
            f"using hardware acceleration: {recommended_hwaccel.value if recommended_hwaccel else 'none'}"
        )

        # always use wav format for audio extraction for better compatibility
        audio_ext = ".wav"  # use wav instead of aac for broad compatibility
        audio_output_file = os.path.join(
            _DEFAULT_INPUT_DIRECTORY, filename + "_audio" + audio_ext
        )

        # extract video without audio using stream copying
        video_output_file = os.path.join(
            _DEFAULT_INPUT_DIRECTORY, filename + "_video.mp4"
        )

        def extract_audio_task():
            """extract audio in a separate thread"""
            try:
                # extract audio to wav format for maximum compatibility with audio processing libraries
                # don't use copy_audio for wav to ensure proper conversion
                ffmpeg.extract_audio(
                    video_file=video_file,
                    audio_output=audio_output_file,
                    copy_audio=False,  # don't copy, convert to ensure format compatibility
                    audio_format="pcm_s16le",  # standard 16-bit pcm wav format
                )

                # if file doesn't exist or is empty, fallback to traditional extraction
                if (
                    not os.path.exists(audio_output_file)
                    or os.path.getsize(audio_output_file) == 0
                ):
                    raise FileNotFoundError("direct audio extraction failed")

                return True

            except Exception as e:
                logger().warning(
                    f"ffmpeg audio extraction failed: {str(e)}. falling back to moviepy."
                )

                # fallback to traditional extraction with moviepy
                with VideoFileClip(video_file) as video_clip, warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=UserWarning)
                    audio_clip = video_clip.audio
                    if audio_clip is not None:
                        audio_clip.write_audiofile(audio_output_file, logger=None)
                    else:
                        # create an empty audio file
                        with open(audio_output_file, "wb") as f:
                            pass  # just create an empty file
                        logger().warning(f"no audio track found in {video_file}")
                return False

        def extract_video_task():
            """extract video in a separate thread"""
            try:
                # try to copy video stream directly
                ffmpeg.extract_video(
                    video_file=video_file, video_output=video_output_file
                )

                # if file doesn't exist or is empty, fallback to traditional extraction
                if (
                    not os.path.exists(video_output_file)
                    or os.path.getsize(video_output_file) == 0
                ):
                    raise FileNotFoundError("direct video copy failed")

                return True

            except Exception as e:
                logger().warning(
                    f"ffmpeg video extraction failed: {str(e)}. falling back to moviepy."
                )

                # fallback to traditional extraction with moviepy
                with VideoFileClip(video_file) as video_clip, warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=UserWarning)
                    video_clip_without_audio = video_clip.with_audio(None)
                    fps = video_clip.fps or _DEFAULT_FPS
                    video_clip_without_audio.write_videofile(
                        video_output_file,
                        codec=VideoProcessing._GPU_ENCODER,
                        fps=fps,
                        logger=None,
                    )
                return False

        # run audio and video extraction in parallel for better performance
        logger().debug("starting parallel audio and video extraction")
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            audio_future = executor.submit(extract_audio_task)
            video_future = executor.submit(extract_video_task)

            # wait for both tasks to complete
            audio_success = audio_future.result()
            video_success = video_future.result()

        logger().debug(
            f"extraction completed - audio: {'success' if audio_success else 'fallback'}, video: {'success' if video_success else 'fallback'}"
        )

        return video_output_file, audio_output_file

    @staticmethod
    def combine_audio_video(
        *,
        video_file: str,
        dubbed_audio_file: str,
        output_directory: str,
        target_language: str,
    ) -> str:
        """combines an audio file with a video file, ensuring they have the same duration.

        returns:
          the path to the output video file with dubbed audio.
        """
        target_language_suffix = "_" + target_language.replace("-", "_").lower()
        dubbed_video_file = os.path.join(
            output_directory,
            _DEFAULT_DUBBED_VIDEO_FILE
            + target_language_suffix
            + _DEFAULT_OUTPUT_FORMAT,
        )

        # check if there's a significant duration difference
        need_duration_adjustment = False
        temp_audio_file = dubbed_audio_file

        try:
            with (
                VideoFileClip(video_file) as video,
                AudioFileClip(dubbed_audio_file) as audio,
            ):
                duration_difference = video.duration - audio.duration
                need_duration_adjustment = (
                    abs(duration_difference) > 0.5
                )  # only adjust if difference is significant

                if need_duration_adjustment:
                    # create a temporary audio file with adjusted duration
                    # use the same extension as the original dubbed audio
                    audio_ext = os.path.splitext(dubbed_audio_file)[1]
                    temp_dir = os.path.dirname(dubbed_audio_file)
                    temp_audio_file = os.path.normpath(
                        os.path.join(temp_dir, f"temp_adjusted_audio{audio_ext}")
                    )

                    if duration_difference > 0:
                        # video longer than audio - add silence
                        import numpy as np
                        from moviepy.audio.AudioClip import AudioArrayClip

                        # create silent audio with the same sample rate as the original audio
                        sample_rate = audio.fps or 44100
                        silence_samples = int(duration_difference * sample_rate)
                        silence_array = np.zeros((silence_samples, 2))  # stereo silence
                        silence = AudioArrayClip(silence_array, fps=sample_rate)
                        combined = concatenate_audioclips([audio, silence])
                        combined.write_audiofile(temp_audio_file, logger=None)
                    else:
                        # audio longer than video - trim audio
                        trimmed = audio.subclip(0, video.duration)
                        trimmed.write_audiofile(temp_audio_file, logger=None)
        except Exception as e:
            logger().warning(
                f"error checking duration difference: {str(e)}. proceeding with direct combination."
            )

        # direct ffmpeg stream copy for final video if possible with hardware acceleration
        recommended_hwaccel = FFmpeg.get_recommended_hwaccel()
        ffmpeg = FFmpeg(recommended_hwaccel)
        logger().debug(
            f"using hardware acceleration for video combination: {recommended_hwaccel.value if recommended_hwaccel else 'none'}"
        )

        try:
            # normalize paths for ffmpeg
            normalized_video_file = os.path.normpath(video_file)
            normalized_audio_file = os.path.normpath(temp_audio_file)
            normalized_output_file = os.path.normpath(dubbed_video_file)

            # try using direct ffmpeg stream copy first (faster)
            ffmpeg.combine_video_audio(
                video_file=normalized_video_file,
                audio_file=normalized_audio_file,
                output_file=normalized_output_file,
                audio_codec="aac",  # always convert to aac for the final video
            )

            # check if output file exists and has content
            if (
                not os.path.exists(dubbed_video_file)
                or os.path.getsize(dubbed_video_file) == 0
            ):
                raise FileNotFoundError("direct combination failed")

        except Exception as e:
            logger().warning(
                f"direct ffmpeg combination failed: {str(e)}. falling back to moviepy."
            )

            # fallback to moviepy if direct combination fails
            with (
                VideoFileClip(video_file) as video,
                AudioFileClip(temp_audio_file) as audio,
            ):
                final_clip = video.with_audio(audio)
                final_clip.write_videofile(
                    dubbed_video_file,
                    codec=VideoProcessing._GPU_ENCODER,
                    audio_codec="aac",
                    temp_audiofile="temp-audio.m4a",
                    remove_temp=True,
                    logger=None,
                )

        # clean up temp file if created
        if (
            need_duration_adjustment
            and temp_audio_file != dubbed_audio_file
            and os.path.exists(temp_audio_file)
        ):
            try:
                os.remove(temp_audio_file)
            except Exception as e:
                logger().warning(f"failed to delete temp audio file: {str(e)}")

        return dubbed_video_file


# initialize gpu acceleration after src definition
VideoProcessing._setup_gpu_acceleration()
