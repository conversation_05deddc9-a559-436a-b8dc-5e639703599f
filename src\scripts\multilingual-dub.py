import os
import re
import sys
import argparse
import logging
import subprocess
import shutil
import signal
import atexit
import threading
from typing import Dict, List, Tuple, Optional, Any
from datetime import timed<PERSON><PERSON>
from pydub import AudioSegment


# determine project root and add to sys.path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(script_dir))
sys.path.append(project_root)

# import project modules after setting path
# noqa: e402 imports used to prevent linting errors before sys.path is modified
from src.utils.logger import logger  # noqa: E402
from src.audio_processing import audio_processing  # noqa: E402

from src.audio_processing.text_to_speech_elevenlabs import (  # noqa: E402
    TextToSpeechElevenLabs,
)
from src.audio_processing.text_to_speech import TextToSpeech  # noqa: E402
from src.audio_processing.voice_gender_classifier import (  # noqa: E402
    VoiceGenderClassifier,
)  # noqa: E402
from src.video_processing.video_processing import VideoProcessing  # noqa: E402
from src.video_processing.ffmpeg import FFmpeg  # noqa: E402


# --- resource management ---

# define more specific types if possible, using any for now where types are complex/external
_open_resources: Dict[str, List[Any]] = {
    "processes": [],
    "threads": [],
    "connections": [],
}
_shutdown_in_progress: bool = False


def _cleanup_process(process: subprocess.Popen):
    """terminate or kill a single subprocess.popen process."""
    try:
        # check if the process is still running
        if process.poll() is None:
            logger().debug(f"terminating subprocess: {process.pid}")
            process.terminate()
            process.wait(timeout=5)  # wait briefly for termination
        if process.poll() is None:  # if still running, force kill
            logger().warning(f"forcing kill on subprocess: {process.pid}")
            process.kill()
    except ProcessLookupError:
        logger().debug(f"process {process.pid} already terminated.")
    except Exception as e:
        logger().error(f"error terminating process {process.pid}: {str(e)}")


def _cleanup_thread(thread: threading.Thread):
    """join a single thread."""
    try:
        if thread.is_alive():
            logger().debug(f"waiting for thread to finish: {thread.name}")
            thread.join(timeout=1.0)
    except Exception as e:
        logger().error(f"error joining thread {thread.name}: {str(e)}")


def _cleanup_connection(conn: Any):
    """close a single connection if possible (e.g., file handles, sockets)."""
    try:
        # check if the object has a 'close' method and it's callable
        close_method = getattr(conn, "close", None)
        if callable(close_method):
            logger().debug(f"closing connection/resource: {conn}")
            close_method()
        else:
            logger().debug(
                f"resource {conn} does not appear to have a standard close() method or it's not callable, skipping."
            )
    except Exception as e:
        logger().error(f"error closing connection {conn}: {str(e)}")


def cleanup_resources():
    """
    cleanup function to ensure all resources are properly closed.
    refactored for lower complexity.
    """
    global _shutdown_in_progress
    if _shutdown_in_progress:
        return
    _shutdown_in_progress = True

    logger().info("cleaning up resources...")

    for process in _open_resources["processes"]:
        _cleanup_process(process)

    for thread in _open_resources["threads"]:
        _cleanup_thread(thread)

    for conn in _open_resources["connections"]:
        _cleanup_connection(conn)

    _open_resources["processes"].clear()
    _open_resources["threads"].clear()
    _open_resources["connections"].clear()

    logger().info("cleanup complete.")


def register_subprocess(process: subprocess.Popen) -> subprocess.Popen:
    """register a subprocess for cleanup."""
    _open_resources["processes"].append(process)
    return process


def register_thread(thread: threading.Thread) -> threading.Thread:
    """register a thread for cleanup."""
    _open_resources["threads"].append(thread)
    return thread


def register_connection(connection: Any) -> Any:
    """register a connection or resource for cleanup."""
    _open_resources["connections"].append(connection)
    return connection


# define signal handler type hint if possible, using any for now
def signal_handler(sig: int, frame: Optional[Any]):  # frame type can be complex
    """handle interrupt signals (ctrl+c)."""
    logger().info(f"received signal {sig}, shutting down...")
    cleanup_resources()
    logger().info("exiting.")
    # use os._exit for immediate exit after cleanup, especially if signal handling is tricky
    os._exit(1 if sig != signal.SIGTERM else 0)


# register the cleanup function to run on exit
atexit.register(cleanup_resources)

# register signal handlers
signal.signal(signal.SIGINT, signal_handler)  # handle ctrl+c
signal.signal(signal.SIGTERM, signal_handler)  # handle termination signal


# --- srt parsing utilities ---

# define a type alias for utterance metadata for clarity
UtteranceData = Dict[str, Any]


def parse_srt_time(time_str: str) -> float:
    """
    convert srt timestamp to seconds.
    example: 00:00:05,556 -> 5.556 or 00:05,736 -> 5.736
    """
    parts = time_str.replace(",", ".").split(":")
    total_seconds = 0.0
    try:
        if len(parts) == 3:  # hh:mm:ss.ms
            hours, minutes, seconds = map(float, parts)
            total_seconds = hours * 3600 + minutes * 60 + seconds
        elif len(parts) == 2:  # mm:ss.ms
            minutes, seconds = map(float, parts)
            total_seconds = minutes * 60 + seconds
        else:
            raise ValueError("invalid time format")
    except ValueError as e:
        raise ValueError(f"unexpected timestamp format in '{time_str}': {e}")

    return total_seconds


def normalize_srt_timestamp(time_str: str) -> str:
    """
    normalize srt timestamp to mm:ss,mmm or hh:mm:ss,mmm format.
    """
    parts = time_str.split(":")
    if len(parts) == 3 and parts[0] == "00":
        # simplify 00:mm:ss,mmm to mm:ss,mmm
        return f"{parts[1]}:{parts[2]}"
    # keep mm:ss,mmm or hh:mm:ss,mmm (where hh != 00) as is
    return time_str


def _fix_timestamp_milliseconds(time_line: str) -> str:
    """ensure milliseconds have 3 digits and correct separators."""
    # fix formats like 00:30:0761 -> 00:30:07,610
    fixed_line = re.sub(
        r"(\d{2}:\d{2}:)(\d{2})(\d{2,3})($|\s)", r"\1\2,\3\4", time_line
    )

    # fix formats like 00:30:07610 --> -> 00:30:07,610 -->
    fixed_line = re.sub(
        r"(\d{2}:\d{2}:\d{2})(\d{3,})(\s*-->)",
        lambda m: f"{m.group(1)},{m.group(2)[:3]}{m.group(3)}",
        fixed_line,
    )

    # ensure milliseconds (after , or .) have exactly 3 digits
    def pad_milliseconds(match: re.Match) -> str:  # add type hint for match
        separator = match.group(1)  # , or .
        digits = match.group(2)  # 1 to 3 digits
        # ensure digits is treated as string for len, handle potential None from group
        digits_str = digits if digits is not None else ""
        padding = "0" * (3 - len(digits_str))
        return f"{separator}{digits_str}{padding}"

    # apply padding to both start and end times if needed
    fixed_line = re.sub(r"([.,])(\d{1,3})(?=\s*-->)", pad_milliseconds, fixed_line)
    fixed_line = re.sub(
        r"-->\s*(.*?:[^.,]*[.,])(\d{1,3})$",
        lambda m: f"--> {m.group(1)}{m.group(2)}{'0'*(3-len(m.group(2)))}",
        fixed_line,
    )

    # replace . with , for consistency
    fixed_line = fixed_line.replace(".", ",")

    return fixed_line


def fix_timestamp_format(time_line: str) -> Tuple[str, bool]:
    """
    attempt to fix common timestamp format issues.
    returns the fixed time line and whether the fix was successful.
    """
    # regex pattern for timestamps (hh:mm:ss,mmm or mm:ss,mmm)
    timestamp_pattern = (
        r"((?:\d{2}:)?\d{2}:\d{2},\d{3})\s*-->\s*((?:\d{2}:)?\d{2}:\d{2},\d{3})"
    )

    try:
        fixed_time_line = _fix_timestamp_milliseconds(time_line)

        # check if the fixed line matches the standard pattern
        match = re.match(timestamp_pattern, fixed_time_line)
        if match:
            if fixed_time_line != time_line:
                logger().debug(
                    f"fixed timestamp format: '{time_line}' -> '{fixed_time_line}'"
                )
            return fixed_time_line, True
        else:
            # if still no match, the format is likely too broken
            logger().warning(f"could not fix timestamp format: '{time_line}'")
            return time_line, False
    except Exception as e:
        logger().error(f"error fixing timestamp '{time_line}': {str(e)}")
        return time_line, False


# define a type alias for utterance metadata for clarity
UtteranceData = Dict[str, Any]


def process_timestamp_block(block: str, lines: List[str]) -> Optional[UtteranceData]:
    """
    process a single srt block and extract timestamp and text information.
    returns none if the block is invalid. refactored for complexity.
    """
    # regex pattern for standard timestamps (hh:mm:ss,mmm or mm:ss,mmm)
    timestamp_pattern = (
        r"((?:\d{2}:)?\d{2}:\d{2},\d{3})\s*-->\s*((?:\d{2}:)?\d{2}:\d{2},\d{3})"
    )

    time_line = lines[1]
    match = re.match(timestamp_pattern, time_line)

    # if no match, try to fix the timestamp format
    if not match:
        time_line, success = fix_timestamp_format(time_line)
        if success:
            match = re.match(timestamp_pattern, time_line)
        else:
            # log warning if fixing failed and format is still invalid
            logger().warning(
                f"skipping block with invalid timestamp format: {block.splitlines()[0]} '{lines[1]}'"
            )
            return None  # skip block if timestamp is unfixable

    # if still no match after trying to fix, skip
    if not match:
        logger().warning(
            f"skipping block due to unparseable timestamp line: {block.splitlines()[0]} '{lines[1]}'"
        )
        return None

    start_time_str, end_time_str = match.groups()

    try:
        start_time = parse_srt_time(start_time_str)
        end_time = parse_srt_time(end_time_str)
    except ValueError as e:
        logger().error(
            f"error parsing time strings '{start_time_str}' or '{end_time_str}': {e}"
        )
        logger().warning(
            f"skipping block due to time parsing error: {block.splitlines()[0]}"
        )
        return None

    if start_time >= end_time:
        logger().warning(
            f"skipping block with invalid time range (start >= end): {block.splitlines()[0]} {start_time_str} --> {end_time_str}"
        )
        return None

    # extract subtitle text (join all lines after the timestamp line)
    text = " ".join(lines[2:]).strip()
    if not text:
        logger().debug(f"skipping block with empty text: {block.splitlines()[0]}")
        return None

    return {
        "start": start_time,
        "end": end_time,
        "text": text,
        "duration": end_time - start_time,
    }


def parse_srt_file(srt_path: str) -> List[UtteranceData]:
    """
    parse an srt file and return a list of utterance metadata.
    """
    logger().info(f"parsing srt file: {srt_path}")
    utterance_metadata: List[UtteranceData] = []  # add type hint to list

    try:
        with open(srt_path, "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        logger().error(f"srt file not found: {srt_path}")
        return []
    except Exception as e:
        logger().error(f"error reading srt file {srt_path}: {e}")
        return []

    # split content by double newline (or more) to get individual subtitle blocks
    blocks = [block.strip() for block in re.split(r"\n\s*\n", content) if block.strip()]

    for i, block in enumerate(blocks):
        lines = block.splitlines()

        # basic validation for a valid block structure
        if len(lines) < 3:
            logger().warning(f"skipping invalid block #{i+1} (too few lines): {block}")
            continue

        # check index line (optional, but good for format check)
        if not lines[0].isdigit():
            logger().warning(
                f"skipping block #{i+1} (invalid index '{lines[0]}'): {block}"
            )
            continue

        # process the timestamp and text
        metadata = process_timestamp_block(block, lines)
        if metadata:
            utterance_metadata.append(metadata)
        # process_timestamp_block handles its own logging for skipping

    logger().info(f"parsed {len(utterance_metadata)} valid utterances from srt file.")
    return utterance_metadata


# --- audio/video processing ---


def extract_audio_background(
    input_video: str, output_dir: str
) -> Tuple[Optional[str], Optional[str]]:
    """
    extract audio from video file and return paths to video and audio files.
    returns (none, none) on failure.
    """
    logger().info(f"extracting audio from video: {input_video}")
    os.makedirs(output_dir, exist_ok=True)

    try:
        video_path, audio_path = VideoProcessing.split_audio_video(
            video_file=input_video, output_directory=output_dir
        )
        # ensure both paths are valid strings and files exist
        if not isinstance(video_path, str) or not os.path.exists(video_path):
            logger().error(
                f"failed to create or find video-only file from {input_video}"
            )
            return None, None
        if not isinstance(audio_path, str) or not os.path.exists(audio_path):
            logger().error(
                f"failed to create or find extracted audio file from {input_video}"
            )
            # clean up video file if audio extraction failed
            if os.path.exists(video_path):
                try:
                    os.remove(video_path)
                except OSError:
                    pass
            return None, None

        # check if audio file is valid and has proper duration
        if os.path.getsize(audio_path) < 1000:  # less than 1KB
            logger().warning(
                f"extracted background audio file is very small: {os.path.getsize(audio_path)} bytes"
            )
            logger().debug(
                "this may indicate the input video has no background audio track"
            )

        # log the extracted paths
        logger().info(f"extracted video (no background audio) to: {video_path}")
        logger().info(f"extracted background audio to: {audio_path}")

        return video_path, audio_path
    except Exception as e:
        logger().exception(f"error during audio/video splitting: {e}")
        return None, None


def _has_meaningful_background_audio(
    audio_file: str,
    # adjusted thresholds: quieter sound (-55dbfs) must be present for a larger portion (15%)
    # of the audio to be considered meaningful background.
    min_volume_db: float = -55.0,  # lowered from -45.0 (quieter sounds ignored)
    min_duration_ratio: float = 0.15,  # increased from 0.05 (needs more non-silent time)
) -> bool:
    """
    analyze audio file to determine if it has meaningful background audio.
    parameters:
    - audio_file: path to the audio file
    - min_volume_db: threshold in dbfs below which audio is considered silent.
    - min_duration_ratio: minimum ratio of non-silent audio (above threshold) to total duration.
    returns:
    - true if the audio likely has meaningful background content worth preserving.
    - false if the audio is likely silent or contains negligible sound.
    """
    logger().info(f"analyzing background audio content: {os.path.basename(audio_file)}")
    if not os.path.exists(audio_file):
        logger().warning(f"audio file not found for analysis: {audio_file}")
        return False  # cannot analyze, assume no meaningful background

    try:
        # check file size first as a quick test
        file_size_bytes = os.path.getsize(audio_file)
        if file_size_bytes < 1000:  # less than 1kb
            logger().warning(
                f"audio file is extremely small ({file_size_bytes} bytes), assuming no meaningful background"
            )
            return False

        # import necessary function directly
        from pydub.silence import detect_nonsilent

        audio = AudioSegment.from_file(audio_file)
        total_duration_ms = len(audio)

        if total_duration_ms <= 100:  # ignore very short files
            logger().info(
                "audio file is very short, assuming no meaningful background."
            )
            return False

        # use pydub's silence detection
        # cast min_volume_db to int as pydub expects an integer threshold
        silence_threshold_int = int(min_volume_db)
        non_silent_chunks = detect_nonsilent(  # call the imported function directly
            audio,
            min_silence_len=500,  # minimum silence length in ms
            silence_thresh=silence_threshold_int,  # use the integer threshold
        )

        if not non_silent_chunks:
            logger().info(
                f"no non-silent audio detected above {silence_threshold_int} dbfs threshold."
            )
            return False

        # calculate total duration of non-silent parts
        non_silent_duration_ms = sum(end - start for start, end in non_silent_chunks)
        non_silent_ratio = non_silent_duration_ms / total_duration_ms

        logger().info(
            f"background audio analysis: non-silent ratio={non_silent_ratio:.4f} (threshold={min_duration_ratio}, volume > {silence_threshold_int} dbfs)"
        )

        has_background = non_silent_ratio >= min_duration_ratio
        if not has_background:
            logger().info(
                "background audio determined to have minimal content, merge will be skipped."
            )
        else:
            logger().info("background audio determined to have meaningful content.")
        return has_background

    except ImportError:  # catch importerror first
        logger().error(
            "pydub.silence not found. please ensure pydub is installed correctly."
        )
        return True  # assume background exists if we can't check
    except Exception as e:  # catch other exceptions afterwards
        logger().warning(
            f"error analyzing audio file {os.path.basename(audio_file)}: {e}. assuming background exists."
        )
        return True  # if analysis fails, assume background exists to be safe


# --- tts utilities ---


def _initialize_tts(tts_provider="elevenlabs") -> Optional[TextToSpeech]:
    """initialize tts and register resources for cleanup."""
    try:
        if tts_provider.lower() == "elevenlabs":
            logger().info("initializing elevenlabs tts...")
            tts = TextToSpeechElevenLabs()
        else:
            logger().error(f"unknown tts provider: {tts_provider}")
            return None
        return tts
    except Exception as e:
        logger().error(f"error initializing tts provider '{tts_provider}': {e}")
        return None


def _cleanup_tts_client(client):
    """attempt to close the http client within a tts client wrapper."""
    if hasattr(client, "_client_wrapper") and hasattr(
        client._client_wrapper, "httpx_client"
    ):
        try:
            http_client = client._client_wrapper.httpx_client
            if hasattr(http_client, "close"):
                # check if it's async or sync client for close method
                if hasattr(http_client, "aclose"):
                    # cannot easily run async close here, rely on httpx cleanup or atexit
                    logger().debug(
                        "async httpx client found, relying on automatic cleanup."
                    )
                else:
                    http_client.close()
                    logger().debug("closed sync httpx client session.")
        except Exception as close_err:
            logger().warning(f"error closing httpx client session: {close_err}")


def _cleanup_tts_resources(tts):
    """clean up tts resources properly."""
    if not tts:
        return
    logger().debug("cleaning up tts resources...")
    try:
        # call specific cleanup method if available
        if hasattr(tts, "cleanup") and callable(tts.cleanup):
            tts.cleanup()

        # specific cleanup for known clients
        if hasattr(tts, "client") and tts.client:
            _cleanup_tts_client(tts.client)

    except Exception as e:
        logger().error(f"error during tts cleanup: {e}")


def _setup_tts_voice(tts: TextToSpeech, target_language, voice_id):
    """helper function to set up the tts voice."""
    if voice_id:
        logger().info(f"using provided voice_id: {voice_id}")
        return voice_id

    logger().info(f"selecting a default voice for language: {target_language}")
    # provider-aware default voice logic
    if isinstance(tts, TextToSpeechElevenLabs):
        selected_voice = tts.DEFAULT_VOICE_ID
        logger().info(f"selected default elevenlabs voice: {selected_voice}")
        return selected_voice

    else:
        logger().error("unknown tts provider type in _setup_tts_voice.")
        return None


def _detect_gender_from_audio(audio_file_path: str) -> Optional[str]:
    """detect gender from audio file, returns class constant or none on error."""
    if not os.path.exists(audio_file_path) or os.path.getsize(audio_file_path) < 1024:
        logger().warning(f"cannot detect gender, audio file invalid: {audio_file_path}")
        return None
    try:
        gender_classifier = VoiceGenderClassifier()
        predicted_gender = gender_classifier.get_gender_for_file(audio_file_path)
        logger().debug(
            f"detected gender '{predicted_gender}' from {os.path.basename(audio_file_path)}"
        )
        if predicted_gender and isinstance(predicted_gender, str):
            if predicted_gender.lower() == "female":
                return TextToSpeechElevenLabs._GENDER_FEMALE
            elif predicted_gender.lower() == "male":
                return TextToSpeechElevenLabs._GENDER_MALE
            else:
                return TextToSpeechElevenLabs._GENDER_UNKNOWN
        return None
    except Exception as e:
        logger().error(
            f"error determining gender from audio {os.path.basename(audio_file_path)}: {e}"
        )
        return None


def _select_voice_by_gender(
    tts: TextToSpeech, gender: str, language_code: str, fallback_voice: str
) -> str:
    """select a voice matching the specified gender, with provider-aware fallback."""
    # force elevenlabs to use hardcoded default ids for gender-based selection
    from src.audio_processing.text_to_speech_elevenlabs import TextToSpeechElevenLabs

    # only log provider class at debug level
    tts_class = type(tts)
    logger().debug(
        f"tts provider class: {tts_class} (isinstance: {isinstance(tts, TextToSpeechElevenLabs)})"
    )
    if tts.__class__.__name__ == "TextToSpeechElevenLabs" or isinstance(
        tts, TextToSpeechElevenLabs
    ):
        if gender == getattr(tts, "_GENDER_FEMALE", "female"):
            return getattr(tts, "DEFAULT_FEMALE_VOICE_ID", fallback_voice)
        elif gender == getattr(tts, "_GENDER_MALE", "male"):
            return getattr(tts, "DEFAULT_MALE_VOICE_ID", fallback_voice)
        else:
            return getattr(tts, "DEFAULT_VOICE_ID", fallback_voice)
    # fallback to original logic for other providers
    if not gender:
        return fallback_voice
    try:
        available_voices = tts.get_available_voices(language_code)
        if not available_voices:
            logger().warning(
                f"no voices available for {language_code} to select by gender."
            )
            return fallback_voice
        matching_voices = [
            v for v in available_voices if v.gender.lower() == gender.lower()
        ]
        if not matching_voices:
            logger().warning(
                f"no voices found matching gender '{gender}' for {language_code}."
            )
            return fallback_voice
        selected_voice = matching_voices[0].name
        return selected_voice
    except Exception as e:
        logger().error(f"error selecting voice by gender: {e}")
        return fallback_voice


def _is_valid_audio_file(file_path: Optional[str], min_size: int = 1024) -> bool:
    """check if an audio file exists and has valid size."""
    if not file_path:
        return False
    return os.path.exists(file_path) and os.path.getsize(file_path) > min_size


def _extract_original_segment(
    original_audio_path: str, start_time: float, end_time: float, output_path: str
) -> bool:
    """extracts a segment from the original audio for analysis."""
    try:
        audio = AudioSegment.from_file(original_audio_path)
        segment = audio[int(start_time * 1000) : int(end_time * 1000)]
        segment.export(output_path, format="mp3")
        return _is_valid_audio_file(
            output_path, min_size=100
        )  # check if export was successful
    except Exception as e:
        logger().error(
            f"failed to extract original audio segment ({start_time}-{end_time}s): {e}"
        )
        return False


def _process_single_utterance(
    tts: TextToSpeech,
    utterance: Dict,
    index: int,
    output_dir: str,
    target_language: str,
    assigned_voice: str,
    skip_existing: bool,
    total_count: int,
    use_dynamic_gender: bool,
    original_audio_path: Optional[str],
) -> Dict:
    """
    process a single utterance: generate tts audio, optionally using dynamic gender.
    returns the updated utterance metadata dictionary.
    """
    utterance_copy = utterance.copy()
    utterance_copy["for_dubbing"] = False

    start_time_str = str(utterance.get("start", index)).replace(".", "_")
    output_filename = os.path.join(
        output_dir, f"dubbed_utterance_{index+1:04d}_start_{start_time_str}.mp3"
    )
    utterance_copy["dubbed_path"] = output_filename

    if skip_existing and _is_valid_audio_file(output_filename):
        logger().info(f"using existing audio for utterance {index+1}/{total_count}")
        utterance_copy["for_dubbing"] = True
        return utterance_copy

    voice_to_use = assigned_voice
    if use_dynamic_gender and original_audio_path:
        segment_output_path = os.path.join(
            output_dir, f"original_segment_{index+1:04d}.mp3"
        )
        if _extract_original_segment(
            original_audio_path,
            utterance["start"],
            utterance["end"],
            segment_output_path,
        ):
            detected_gender = _detect_gender_from_audio(segment_output_path)
            if detected_gender:
                voice_to_use = _select_voice_by_gender(
                    tts, detected_gender, target_language, assigned_voice
                )
                # log only if gender or voice changed (store as function attributes)
                if not hasattr(
                    _process_single_utterance, "_last_gender"
                ) or not hasattr(_process_single_utterance, "_last_voice"):
                    _process_single_utterance._last_gender = None
                    _process_single_utterance._last_voice = None
                if (
                    detected_gender != _process_single_utterance._last_gender
                    or voice_to_use != _process_single_utterance._last_voice
                ):
                    logger().info(
                        f"selected voice for gender '{detected_gender}': {voice_to_use}"
                    )
                    _process_single_utterance._last_gender = detected_gender
                    _process_single_utterance._last_voice = voice_to_use
            # clean up segment file if not keeping temp files (handled later in main cleanup)
        else:
            logger().warning(
                f"could not extract/analyze original segment for utterance {index+1}, using default voice."
            )

    # generate new speech file
    try:
        # note: speed parameter might not be supported by all providers or implementations
        speed = 1.0  # default speed

        result_path = tts._convert_text_to_speech(
            assigned_voice=voice_to_use,
            target_language=target_language,
            output_filename=output_filename,
            text=utterance["text"],
            speed=speed,
        )

        # verify the generated file is valid
        if _is_valid_audio_file(result_path):
            logger().debug(f"generated audio for utterance {index+1}/{total_count}")
            utterance_copy["dubbed_path"] = result_path  # confirm path
            utterance_copy["for_dubbing"] = True
        else:
            logger().warning(
                f"audio file missing or invalid after generation for utterance {index+1}: {result_path}"
            )
            # keep for_dubbing=false

    except Exception as e:
        logger().error(f"error generating speech for utterance {index+1}: {e}")
        # keep for_dubbing=false

    return utterance_copy


def perform_text_to_speech(
    utterance_metadata: List[UtteranceData],
    output_dir: str,
    target_language: str,
    original_audio_path: Optional[str],  # needed for gender detection
    voice_id: Optional[str] = None,
    skip_existing: bool = True,
    tts_provider: str = "elevenlabs",
    use_dynamic_gender: bool = False,
) -> List[UtteranceData]:
    """
    generate speech for each text segment using the specified tts provider.
    handles voice selection based on cli args or dynamic gender detection.
    returns an updated list of utterance metadata, or an empty list on critical failure.
    """
    tts = _initialize_tts(tts_provider=tts_provider)
    if not tts:
        logger().error("failed to initialize tts. cannot generate speech.")
        return []

    initial_assigned_voice = _setup_tts_voice(tts, target_language, voice_id)
    if not initial_assigned_voice:
        logger().error("failed to setup tts voice. cannot generate speech.")
        _cleanup_tts_resources(tts)
        return []

    total_count = len(utterance_metadata)
    logger().info(
        f"generating speech for {total_count} utterances using {tts_provider}..."
    )
    if use_dynamic_gender:
        logger().info("dynamic gender detection enabled.")
        if not original_audio_path:
            logger().warning(
                "dynamic gender detection enabled, but original audio path is missing. cannot perform detection."
            )
            use_dynamic_gender = False

    updated_metadata: List[UtteranceData] = []
    try:
        for i, utterance in enumerate(utterance_metadata):
            if not utterance.get("text", "").strip():
                logger().debug(f"skipping utterance {i+1} due to empty text.")
                utterance_copy = utterance.copy()
                utterance_copy["for_dubbing"] = False
                updated_metadata.append(utterance_copy)
                continue

            processed_utterance = _process_single_utterance(
                tts=tts,
                utterance=utterance,
                index=i,
                output_dir=output_dir,
                target_language=target_language,
                assigned_voice=initial_assigned_voice,
                skip_existing=skip_existing,
                total_count=total_count,
                use_dynamic_gender=use_dynamic_gender,
                original_audio_path=original_audio_path,
            )
            updated_metadata.append(processed_utterance)

    except Exception as e:
        logger().exception(f"unhandled exception during tts processing loop: {e}")
        _cleanup_tts_resources(tts)
        return updated_metadata

    finally:
        _cleanup_tts_resources(tts)

    valid_tts_count = sum(1 for u in updated_metadata if u.get("for_dubbing"))
    logger().info(
        f"tts processing complete. {valid_tts_count}/{total_count} utterances have valid audio."
    )
    return updated_metadata


# --- dubbed audio/video creation ---


def _verify_file_validity(file_path: Optional[str], min_size: int = 1000) -> bool:
    """verify a file exists and has sufficient size."""
    if not file_path:
        return False
    return os.path.exists(file_path) and os.path.getsize(file_path) >= min_size


def direct_insert_audio_at_timestamps(
    utterance_metadata: List[UtteranceData],
    background_audio_file: str,
    output_directory: str,
) -> Optional[str]:
    """
    custom implementation of inserting audio at timestamps using pydub.
    """
    logger().info("inserting audio segments using pydub...")
    os.makedirs(output_directory, exist_ok=True)
    dubbed_vocals_file = os.path.join(output_directory, "dubbed_vocals.mp3")

    try:
        # load background to get duration, handle potential errors
        try:
            background_audio = AudioSegment.from_file(background_audio_file)
            background_duration_ms = len(background_audio)
        except Exception as e:
            logger().error(
                f"failed to load background audio {background_audio_file}: {e}. cannot determine duration."
            )
            # fallback: estimate duration from last utterance end time? or fail?
            last_utterance = (
                max(utterance_metadata, key=lambda u: u.get("end", 0.0))
                if utterance_metadata
                else None
            )  # provide default for get
            if (
                last_utterance and last_utterance.get("end") is not None
            ):  # check if 'end' exists and is not None
                background_duration_ms = (
                    int(last_utterance["end"] * 1000) + 5000
                )  # add 5s buffer
                logger().warning(
                    f"estimating background duration as {background_duration_ms / 1000.0:.2f}s based on last utterance."
                )
            else:
                logger().error(
                    "cannot determine background duration. aborting audio insertion."
                )
                return None

        # create a blank audio segment of the determined length
        blank_audio = AudioSegment.silent(duration=background_duration_ms)
        inserted_count = 0

        # overlay each valid dubbed segment
        for utterance in utterance_metadata:
            if not utterance.get("for_dubbing", False):
                continue

            dubbed_path = utterance.get("dubbed_path")  # dubbed_path is Optional[str]
            # check if dubbed_path is not none before using it
            if not _is_valid_audio_file(dubbed_path):
                logger().warning(
                    f"skipping utterance {utterance.get('start')}: invalid/missing audio file {dubbed_path}"
                )
                continue

            try:
                # ensure dubbed_path is a string before passing to from_file
                if dubbed_path:
                    audio_segment = AudioSegment.from_file(dubbed_path)
                    position_ms = int(utterance["start"] * 1000)
                else:
                    # this case should ideally not happen due to _is_valid_audio_file check, but handle defensively
                    logger().error(
                        f"internal error: dubbed_path is none for utterance {utterance.get('start')}"
                    )
                    continue

                # ensure segment doesn't start before 0 or extend beyond background duration
                if position_ms < 0:
                    position_ms = 0
                # overlay handles clipping if segment goes past the end of blank_audio

                blank_audio = blank_audio.overlay(audio_segment, position=position_ms)
                inserted_count += 1
                # use os.path.basename only if dubbed_path is not none
                log_filename = (
                    os.path.basename(dubbed_path) if dubbed_path else "unknown"
                )
                logger().debug(f"inserted audio at {position_ms}ms: {log_filename}")
            except Exception as e:
                log_filename_err = (
                    os.path.basename(dubbed_path) if dubbed_path else "unknown"
                )
                logger().error(f"error inserting audio segment {log_filename_err}: {e}")
                continue  # skip this segment

        if inserted_count == 0:
            logger().error("no valid audio segments were inserted.")
            return None

        # export the result
        logger().info(
            f"exporting combined vocals track with {inserted_count} segments..."
        )
        blank_audio.export(dubbed_vocals_file, format="mp3", bitrate="192k")

        if not _verify_file_validity(dubbed_vocals_file, min_size=1000):  # check size
            logger().error(
                f"failed to create valid dubbed vocals file: {dubbed_vocals_file}"
            )
            return None

        logger().info(f"created dubbed vocals file: {dubbed_vocals_file}")
        return dubbed_vocals_file

    except Exception as e:
        logger().exception(f"error creating dubbed vocals track: {e}")
        return None


def _copy_vocals_to_final_audio(
    dubbed_vocals_file: str, dubbed_audio_file: str
) -> bool:
    """copy vocals file to final audio file path as fallback."""
    logger().info(
        f"copying vocals to final audio path: {os.path.basename(dubbed_audio_file)}"
    )
    try:
        if not _verify_file_validity(dubbed_vocals_file):
            logger().error(
                f"source vocals file is invalid, cannot copy: {dubbed_vocals_file}"
            )
            return False
        os.makedirs(os.path.dirname(dubbed_audio_file), exist_ok=True)
        shutil.copy2(dubbed_vocals_file, dubbed_audio_file)
        if not _verify_file_validity(dubbed_audio_file):
            logger().error(f"failed to verify copied audio file: {dubbed_audio_file}")
            return False
        logger().info(
            f"successfully copied vocals to: {os.path.basename(dubbed_audio_file)}"
        )
        return True
    except Exception as e:
        logger().error(f"error copying vocals to audio file: {e}")
        return False


def _try_merge_audio(
    background_audio_file: str,
    dubbed_vocals_file: str,
    output_dir: str,
    target_language: str,
    vocals_volume: float,
    background_volume: float,
    dubbed_audio_file: str,  # explicit output path
) -> bool:
    """attempt to merge background and vocals using audio_processing module."""
    try:
        logger().info("attempting to merge background audio and dubbed vocals...")

        # verify inputs before calling merge
        if not _verify_file_validity(background_audio_file):
            logger().error(
                f"invalid background audio file for merge: {background_audio_file}"
            )
            return False

        if not _verify_file_validity(dubbed_vocals_file):
            logger().error(
                f"invalid dubbed vocals file for merge: {dubbed_vocals_file}"
            )
            return False

        # check if background audio is too short to be useful
        try:
            background_audio = AudioSegment.from_file(background_audio_file)
            background_duration = len(background_audio) / 1000.0  # in seconds
            logger().info(f"background audio: duration={background_duration:.2f}s")

            vocals_audio = AudioSegment.from_file(dubbed_vocals_file)
            vocals_duration = len(vocals_audio) / 1000.0  # in seconds
            logger().info(f"vocals audio: duration={vocals_duration:.2f}s")

            # if background is less than 1 second or less than 1% of vocals length
            if (
                background_duration < 1.0
                or background_duration < 0.01 * vocals_duration
            ):
                logger().warning(
                    f"background audio ({background_duration:.2f}s) is too short compared to vocals ({vocals_duration:.2f}s)"
                )
                logger().warning("skipping background merge and using vocals directly")

                # copy vocals to output instead of merging
                vocals_audio.export(dubbed_audio_file, format="mp3", bitrate="192k")
                if _verify_file_validity(dubbed_audio_file):
                    logger().info(
                        f"used vocals directly as output audio: {dubbed_audio_file}"
                    )
                    return True
                else:
                    logger().error(f"failed to export vocals to {dubbed_audio_file}")
                    return False
        except Exception as e:
            logger().warning(
                f"error checking duration difference: {e}. proceeding with direct combination."
            )

        # store expected durations for post-merge validation
        expected_duration_secs = vocals_duration

        # log file sizes before merging
        try:
            bg_file_size = os.path.getsize(background_audio_file)
            vocals_file_size = os.path.getsize(dubbed_vocals_file)
            logger().debug(
                f"pre-merge file sizes: background={bg_file_size/1024:.1f}kb, vocals={vocals_file_size/1024:.1f}kb"
            )
        except Exception as e:
            logger().debug(f"error checking file sizes: {e}")

        # call the merge function
        result_file = audio_processing.merge_background_and_vocals(
            background_audio_file=background_audio_file,
            dubbed_vocals_audio_file=dubbed_vocals_file,
            output_directory=output_dir,
            target_language=target_language,  # used for naming output in the function
            vocals_volume_adjustment=vocals_volume,
            background_volume_adjustment=background_volume,
        )

        # check if the returned path matches the expected path
        expected_basename = os.path.basename(dubbed_audio_file)
        if os.path.basename(result_file) != expected_basename:
            logger().warning(
                f"merge function returned unexpected path '{result_file}', expected '.../{expected_basename}'. attempting rename."
            )
            if os.path.exists(result_file):
                try:
                    shutil.move(result_file, dubbed_audio_file)
                    logger().info(f"renamed merged file to {expected_basename}")
                except Exception as move_err:
                    logger().error(f"failed to rename merged file: {move_err}")
                    return False  # rename failed, consider merge failed
            else:
                logger().error("merge function returned path that does not exist.")
                return False  # file doesn't exist

        # verify the final merged file
        if not _verify_file_validity(dubbed_audio_file):
            logger().error(
                f"merged audio file is invalid or too small: {dubbed_audio_file}"
            )
            return False

        # validate the merged file's duration to ensure it wasn't truncated
        try:
            merged_audio = AudioSegment.from_file(dubbed_audio_file)
            merged_duration = len(merged_audio) / 1000.0  # in seconds
            merged_file_size = os.path.getsize(dubbed_audio_file)
            logger().info(
                f"merged audio: duration={merged_duration:.2f}s, size={merged_file_size/1024:.1f}kb"
            )

            # if merged file is suspiciously short, reject it and use vocals directly
            if merged_duration < 0.5 * expected_duration_secs:
                logger().error(
                    f"background audio is suspiciously short: {merged_duration:.2f}s vs expected ~{expected_duration_secs:.2f}s"
                )
                logger().warning(
                    "rejecting background audio and using vocals directly as fallback"
                )

                # copy vocals to output as fallback
                vocals_audio.export(dubbed_audio_file, format="mp3", bitrate="192k")
                if _verify_file_validity(dubbed_audio_file):
                    logger().info(
                        f"used vocals directly as output audio after merge rejection: {dubbed_audio_file}"
                    )
                    return True
                else:
                    logger().error(
                        f"failed to export vocals to {dubbed_audio_file} after merge rejection"
                    )
                    return False
        except Exception as validate_err:
            logger().warning(f"error validating merged audio duration: {validate_err}")
            # continue anyway since the file might still be usable

        logger().info("successfully merged background and vocals.")
        return True

    except Exception as e:
        logger().error(f"error merging background and vocals: {e}")
        return False


def direct_ffmpeg_combine(
    video_file: str, audio_file: str, output_file: str, audio_codec: str = "aac"
) -> bool:
    """
    directly combine audio and video using ffmpeg command-line.
    returns true if successful, false otherwise.
    """
    logger().info("combining video and audio using direct ffmpeg command...")
    try:
        if not _verify_file_validity(video_file, min_size=10000):
            logger().error(f"input video file invalid for ffmpeg combine: {video_file}")
            return False
        if not _verify_file_validity(audio_file, min_size=1000):
            logger().error(f"input audio file invalid for ffmpeg combine: {audio_file}")
            return False

        # ensure output directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # check if files actually exist at the specified paths (additional validation)
        if not os.path.exists(video_file):
            logger().error(f"video file does not exist at path: {video_file}")
            return False

        if not os.path.exists(audio_file):
            logger().error(f"audio file does not exist at path: {audio_file}")
            return False

        # convert paths to absolute paths to avoid any directory navigation issues
        video_file_abs = os.path.abspath(video_file)
        audio_file_abs = os.path.abspath(audio_file)
        output_file_abs = os.path.abspath(output_file)

        logger().debug(
            f"using absolute paths for ffmpeg: video={video_file_abs}, audio={audio_file_abs}"
        )

        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-loglevel",
            "error",  # less verbose
            "-y",  # overwrite output
            "-i",
            video_file_abs,
            "-i",
            audio_file_abs,
            "-c:v",
            "copy",  # copy video stream
            "-c:a",
            audio_codec,  # re-encode audio
            "-map",
            "0:v:0",  # map video from first input
            "-map",
            "1:a:0",  # map audio from second input
            "-shortest",  # finish encoding when the shortest input stream ends
            output_file_abs,
        ]

        logger().debug(f"running ffmpeg command: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding="utf-8",
            errors="replace",
        )
        register_subprocess(process)  # ensure cleanup

        try:
            _, stderr = process.communicate(timeout=600)  # 10-minute timeout
            if process.returncode != 0:
                logger().error(
                    f"ffmpeg direct combine error (retcode {process.returncode}): {stderr}"
                )
                # attempt to clean up potentially broken output file
                if os.path.exists(output_file):
                    os.remove(output_file)

                # check if the error is about the temp file
                if (
                    "error opening input files: no such file or directory" in stderr
                    and "temp_adjusted_audio.mp3" in stderr
                ):
                    logger().warning(
                        "detected error with temp_adjusted_audio.mp3, attempting alternative approach"
                    )
                    # try again with only the direct input files we've verified
                    return _fallback_ffmpeg_combine(
                        video_file_abs, audio_file_abs, output_file_abs, audio_codec
                    )

                return False
        except subprocess.TimeoutExpired:
            logger().error("ffmpeg direct combine process timed out, terminating.")
            process.terminate()
            try:
                process.wait(timeout=5)  # wait briefly
            except subprocess.TimeoutExpired:
                process.kill()  # force kill if terminate fails
            if os.path.exists(output_file):
                os.remove(output_file)  # clean up partial file
            return False

        # verify output
        if not _verify_file_validity(output_file, min_size=10000):  # check size
            logger().error(
                f"direct ffmpeg combine output file missing or too small: {output_file}"
            )
            return False

        logger().info(
            f"successfully created video using direct ffmpeg: {os.path.basename(output_file)}"
        )
        return True

    except Exception as e:
        logger().exception(f"unexpected error in direct ffmpeg combine: {e}")
        return False


def _fallback_ffmpeg_combine(
    video_file: str, audio_file: str, output_file: str, audio_codec: str = "aac"
) -> bool:
    """alternative approach when the standard ffmpeg combine fails with missing temp file errors."""
    logger().info("attempting alternative ffmpeg approach...")

    try:
        # simpler command without complex mappings
        cmd = [
            "ffmpeg",
            "-hide_banner",
            "-loglevel",
            "error",
            "-y",
            "-i",
            video_file,
            "-i",
            audio_file,
            "-c:v",
            "copy",
            "-c:a",
            audio_codec,
            output_file,
        ]

        logger().debug(f"running alternative ffmpeg command: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding="utf-8",
            errors="replace",
        )
        register_subprocess(process)

        _stdout, stderr = process.communicate(timeout=600)

        if process.returncode != 0:
            logger().error(
                f"alternative ffmpeg combine failed (code {process.returncode}): {stderr}"
            )
            return False

        if _verify_file_validity(output_file, min_size=10000):
            logger().info("alternative ffmpeg approach succeeded")
            return True
        else:
            logger().error("alternative ffmpeg produced invalid output file")
            return False

    except Exception as e:
        logger().error(f"error in fallback ffmpeg combine: {e}")
        return False


def _combine_video_and_audio(
    video_file: str, dubbed_audio_file: str, output_dir: str, target_language: str
) -> Optional[str]:
    """try different approaches to combine video and audio, return path if successful."""
    target_language_suffix = "_" + target_language.replace("-", "_").lower()
    dubbed_video_file = os.path.join(
        output_dir, f"dubbed_video{target_language_suffix}.mp4"
    )

    # --- attempt 1: use videoprocessing src ---
    logger().info("combining audio and video (attempt 1: videoprocessing module)...")
    try:
        # ensure inputs exist and are valid before calling
        if not os.path.exists(video_file):
            logger().error(f"input video file doesn't exist: {video_file}")
            raise FileNotFoundError(f"missing video file: {video_file}")

        if not os.path.exists(dubbed_audio_file):
            logger().error(f"input audio file doesn't exist: {dubbed_audio_file}")
            raise FileNotFoundError(f"missing audio file: {dubbed_audio_file}")

        # log absolute paths for debugging
        logger().debug(
            f"combining - video: {os.path.abspath(video_file)} ({os.path.getsize(video_file)/1024:.1f}kb)"
        )
        logger().debug(
            f"combining - audio: {os.path.abspath(dubbed_audio_file)} ({os.path.getsize(dubbed_audio_file)/1024:.1f}kb)"
        )

        # call the videoprocessing function
        output_path = VideoProcessing.combine_audio_video(
            video_file=video_file,
            dubbed_audio_file=dubbed_audio_file,
            output_directory=output_dir,
            target_language=target_language,  # used for naming
        )

        # ensure output_path is a string
        if not isinstance(output_path, str):
            logger().warning(
                f"videoprocessing returned non-string path: {type(output_path)}"
            )
            # try our expected path instead
            output_path = dubbed_video_file

        # verify output path and file validity
        expected_basename = os.path.basename(dubbed_video_file)
        if os.path.basename(output_path) != expected_basename:
            logger().warning(
                f"videoprocessing returned unexpected path '{output_path}', expected '.../{expected_basename}'."
            )
            # check if the file exists at the expected path
            if os.path.exists(dubbed_video_file) and _verify_file_validity(
                dubbed_video_file, min_size=10000
            ):
                logger().info(
                    f"found valid video at expected path: {dubbed_video_file}"
                )
                return os.path.abspath(dubbed_video_file)

        # ensure the output file actually exists
        if not os.path.exists(output_path):
            logger().warning(f"videoprocessing output doesn't exist at: {output_path}")
            # check if it exists at our expected path instead
            if os.path.exists(dubbed_video_file) and output_path != dubbed_video_file:
                logger().info(
                    f"found video at expected path instead: {dubbed_video_file}"
                )
                return os.path.abspath(dubbed_video_file)
            else:
                logger().error("videoprocessing failed to create a valid output file")
                raise FileNotFoundError(f"missing output video file: {output_path}")

        if _verify_file_validity(output_path, min_size=10000):
            logger().info(
                f"successfully created video using videoprocessing. {output_path}"
            )

            # return absolute path to avoid any directory confusion
            abs_path = os.path.abspath(output_path)
            logger().debug(f"returning absolute video path: {abs_path}")
            return abs_path
        else:
            logger().warning(
                f"videoprocessing created file but it seems invalid: {output_path}"
            )
            raise ValueError(f"invalid output file: {output_path}")
    except Exception as e:
        logger().error(f"error combining with videoprocessing: {e}")

    # --- attempt 2: use direct ffmpeg command ---
    logger().info("combining audio and video (attempt 2: direct ffmpeg)...")
    abs_dubbed_video_file = os.path.abspath(dubbed_video_file)
    if direct_ffmpeg_combine(
        video_file=video_file,
        audio_file=dubbed_audio_file,
        output_file=abs_dubbed_video_file,  # use absolute path
    ):
        # direct_ffmpeg_combine already verifies the output
        logger().debug(f"direct ffmpeg created video at: {abs_dubbed_video_file}")
        return abs_dubbed_video_file
    else:
        logger().warning("direct ffmpeg combination failed.")

    # --- attempt 3: use ffmpeg class ---
    logger().info("combining audio and video (attempt 3: ffmpeg class)...")
    try:
        ffmpeg = FFmpeg()
        ffmpeg.combine_video_audio(
            video_file=video_file,
            audio_file=dubbed_audio_file,
            output_file=dubbed_video_file,  # specify output file
            audio_codec="aac",
        )
        if _verify_file_validity(dubbed_video_file, min_size=10000):
            logger().info("successfully created video using ffmpeg class.")
            return os.path.abspath(dubbed_video_file)
        else:
            logger().warning(
                f"ffmpeg class created file but it seems invalid: {dubbed_video_file}"
            )
    except Exception as e:
        logger().error(f"error combining with ffmpeg class: {e}")

    # --- attempt 4: last resort with manual ffmpeg command ---
    logger().info("combining audio and video (attempt 4: manual ffmpeg)...")
    try:
        # ensure inputs exist
        if not os.path.exists(video_file) or not os.path.exists(dubbed_audio_file):
            logger().error("input files missing, can't perform manual combine")
            return None

        # manual ffmpeg command as absolute last resort
        abs_video_file = os.path.abspath(video_file)
        abs_audio_file = os.path.abspath(dubbed_audio_file)
        abs_output_file = os.path.abspath(dubbed_video_file)

        cmd = [
            "ffmpeg",
            "-y",
            "-i",
            abs_video_file,
            "-i",
            abs_audio_file,
            "-c:v",
            "copy",
            "-c:a",
            "aac",
            "-shortest",
            abs_output_file,
        ]

        logger().debug(f"running manual ffmpeg command: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        _, stderr = process.communicate(timeout=300)

        if process.returncode == 0 and _verify_file_validity(
            abs_output_file, min_size=10000
        ):
            logger().info("successfully created video using manual ffmpeg command")
            return abs_output_file
        else:
            logger().error(f"manual ffmpeg command failed: {stderr}")
    except Exception as e:
        logger().error(f"error with manual ffmpeg combine: {e}")

    logger().error("all attempts to combine video and audio failed.")
    return None


def create_dubbed_video(
    utterance_metadata: List[Dict],
    video_only_file: str,  # path to video without original audio
    original_audio_file: str,  # path to original extracted audio
    output_dir: str,
    target_language: str,
    vocals_volume: float = 5.0,
    background_volume: float = -10.0,
    skip_background_if_error: bool = True,
    no_background: bool = False,
    force_vocals_only: bool = False,
) -> Tuple[Optional[str], Optional[str]]:
    """
    create dubbed video by generating vocals, optionally merging with background, and combining with video.
    returns (path_to_final_video, path_to_final_audio) or (none, none).
    """
    logger().info(
        f"starting creation of dubbed video for language '{target_language}'..."
    )

    # filter utterances that have valid tts output
    valid_utterances = [u for u in utterance_metadata if u.get("for_dubbing")]
    if not valid_utterances:
        logger().error(
            "no valid utterances with generated audio found. cannot create dubbed video."
        )
        return None, None
    logger().info(f"{len(valid_utterances)} utterances marked for dubbing.")

    # step 1: create the dubbed vocals track
    dubbed_vocals_file = direct_insert_audio_at_timestamps(
        utterance_metadata=valid_utterances,
        background_audio_file=original_audio_file,  # use original audio for duration reference
        output_directory=output_dir,
    )
    if not _verify_file_validity(dubbed_vocals_file):
        logger().error("failed to create the dubbed vocals track.")
        return None, None

    # step 2: prepare the final audio track (either vocals only or merged)
    target_language_suffix = "_" + target_language.replace("-", "_").lower()
    final_audio_file = os.path.join(
        output_dir, f"dubbed_audio{target_language_suffix}.mp3"
    )
    audio_created_successfully = False

    # check if the original audio exists and has a valid size
    has_valid_background = (
        os.path.exists(original_audio_file)
        and os.path.getsize(original_audio_file) > 5000
    )

    # verify both audio durations to catch inconsistencies early
    try:
        background_audio = AudioSegment.from_file(original_audio_file)
        background_duration = len(background_audio) / 1000.0  # in seconds

        vocals_audio = AudioSegment.from_file(dubbed_vocals_file)
        vocals_duration = len(vocals_audio) / 1000.0  # in seconds

        # if background is less than 10% of vocals length, consider it invalid
        if background_duration < 0.1 * vocals_duration:
            logger().warning(
                f"background audio ({background_duration:.2f}s) is less than 10% of vocals duration ({vocals_duration:.2f}s)"
            )
            logger().warning("considering background audio invalid for merging")
            has_valid_background = False
    except Exception as e:
        logger().warning(f"error checking audio durations: {e}")
        # continue with existing has_valid_background value

    # determine whether to use background or not
    should_skip_background = (
        no_background or force_vocals_only or not has_valid_background
    )

    if should_skip_background:
        if no_background:
            reason = "requested (--no-background)"
        elif force_vocals_only:
            reason = "forced (--force-vocals-only)"
        elif not has_valid_background:
            reason = "invalid background audio file"
        else:
            reason = "unknown reason"
        logger().info(f"skipping background audio merge: {reason}")
        audio_created_successfully = _copy_vocals_to_final_audio(
            dubbed_vocals_file, final_audio_file
        )
    else:
        # try merging
        merge_success = _try_merge_audio(
            background_audio_file=original_audio_file,
            dubbed_vocals_file=dubbed_vocals_file,
            output_dir=output_dir,
            target_language=target_language,
            vocals_volume=vocals_volume,
            background_volume=background_volume,
            dubbed_audio_file=final_audio_file,  # pass the target path
        )
        if merge_success:
            audio_created_successfully = True
        elif skip_background_if_error:
            logger().warning(
                "merging failed. falling back to using vocals only (skip_background_if_error=true)."
            )
            audio_created_successfully = _copy_vocals_to_final_audio(
                dubbed_vocals_file, final_audio_file
            )
        else:
            logger().error(
                "merging failed and skip_background_if_error is false. cannot create final audio."
            )
            # keep audio_created_successfully = false

    if not audio_created_successfully:
        logger().error("failed to create the final audio track.")
        # cleanup vocals file if it exists
        if os.path.exists(dubbed_vocals_file):
            try:
                os.remove(dubbed_vocals_file)
            except OSError:
                pass
        return None, None

    # step 3: combine the video-only file with the final audio track
    final_video_file = _combine_video_and_audio(
        video_file=video_only_file,
        dubbed_audio_file=final_audio_file,
        output_dir=output_dir,
        target_language=target_language,
    )

    if not final_video_file:
        logger().error("failed to combine video and final audio.")
        # cleanup audio file if it exists
        if os.path.exists(final_audio_file):
            try:
                os.remove(final_audio_file)
            except OSError:
                pass
        return None, None

    logger().info(
        f"successfully created final dubbed video: {os.path.basename(final_video_file)}"
    )
    logger().info(
        f"final audio track available at: {os.path.basename(final_audio_file)}"
    )

    return final_video_file, final_audio_file


# --- subtitle handling ---


def format_srt_time(seconds: float) -> str:
    """format seconds as hh:mm:ss,mmm for srt files."""
    if seconds < 0:
        seconds = 0.0  # ensure non-negative time
    # use timedelta for robust calculation
    td = timedelta(seconds=seconds)
    total_seconds = int(td.total_seconds())
    milliseconds = int(td.microseconds / 1000)  # get milliseconds part

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    secs = total_seconds % 60

    return f"{hours:02}:{minutes:02}:{secs:02},{milliseconds:03}"


def save_subtitles(
    utterance_metadata: List[Dict], output_dir: str, target_language: str
) -> Optional[str]:
    """save subtitles to srt file."""
    srt_file_path = os.path.join(output_dir, f"{target_language}.srt")
    logger().info(f"saving subtitles to: {srt_file_path}")
    count = 0
    try:
        with open(srt_file_path, "w", encoding="utf-8") as f:
            for i, utterance in enumerate(utterance_metadata):
                text = utterance.get("text", "").strip()
                if not text:
                    logger().debug(f"skipping subtitle entry {i+1} due to empty text.")
                    continue

                # ensure start/end times are present
                if "start" not in utterance or "end" not in utterance:
                    logger().warning(
                        f"skipping subtitle entry {i+1} due to missing time data."
                    )
                    continue

                start_time_str = format_srt_time(utterance["start"])
                end_time_str = format_srt_time(utterance["end"])

                # write srt block
                f.write(f"{i + 1}\n")
                f.write(f"{start_time_str} --> {end_time_str}\n")
                f.write(f"{text}\n\n")
                count += 1

        if count > 0:
            logger().info(f"saved {count} subtitle entries to: {srt_file_path}")
            return srt_file_path
        else:
            logger().warning("no valid subtitle entries found to save.")
            # clean up empty file
            if os.path.exists(srt_file_path):
                os.remove(srt_file_path)
            return None
    except Exception as e:
        logger().error(f"failed to save subtitles: {e}")
        return None


def add_subtitles_to_video(
    video_file: str, subtitles_file: str, language: str
) -> Optional[str]:
    """
    add subtitles to video using ffmpeg, returns path to new video if successful.
    """
    if not _verify_file_validity(video_file, min_size=10000):
        logger().warning(f"cannot add subtitles: input video invalid: {video_file}")
        return None
    if not _verify_file_validity(subtitles_file, min_size=10):
        logger().warning(
            f"cannot add subtitles: input subtitles invalid: {subtitles_file}"
        )
        return None

    logger().info(
        f"embedding subtitles ({language}) from {os.path.basename(subtitles_file)} into video..."
    )

    base, ext = os.path.splitext(video_file)
    output_video_with_subs = f"{base}_with_subs{ext}"

    # create a temporary copy to work on, preserving the original
    temp_video_for_subs = f"{base}_temp_subs_embed{ext}"
    try:
        shutil.copy2(video_file, temp_video_for_subs)
        logger().debug(
            f"created temporary copy for subtitle embedding: {temp_video_for_subs}"
        )
    except Exception as e:
        logger().error(f"failed to create temporary copy of video: {e}")
        return None

    try:
        ffmpeg = FFmpeg(FFmpeg.get_recommended_hwaccel())
        # note: the ffmpeg class method might modify in-place or return a new path
        # assuming it modifies the input file path provided
        ffmpeg.embed_subtitles(
            video_file=temp_video_for_subs,  # operate on the copy
            subtitles_files=[subtitles_file],
            languages_iso_639_3=[language],  # use language code for metadata
        )

        # check if the temp file was modified and is valid
        if not _verify_file_validity(temp_video_for_subs, min_size=10000):
            logger().error("subtitle embedding failed or produced invalid temp file.")
            raise RuntimeError("subtitle embedding failed")

        # move the modified temp file to the final output name
        shutil.move(temp_video_for_subs, output_video_with_subs)
        logger().info(
            f"subtitles embedded successfully. output: {os.path.basename(output_video_with_subs)}"
        )
        return output_video_with_subs

    except Exception as e:
        logger().error(f"error embedding subtitles: {e}")
        logger().info(f"subtitles file is available separately at: {subtitles_file}")
        return None  # indicate failure but keep original video and srt
    finally:
        # ensure temporary file is removed if it still exists
        if os.path.exists(temp_video_for_subs):
            try:
                os.remove(temp_video_for_subs)
                logger().debug(
                    f"removed temporary subtitle embedding file: {temp_video_for_subs}"
                )
            except OSError as e:
                logger().warning(
                    f"could not remove temp file {temp_video_for_subs}: {e}"
                )


# --- main execution logic ---


def parse_arguments():
    """parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="flexible dubbing script for videos with existing transcriptions."
    )
    # inputs
    parser.add_argument(
        "--input-video", "-i", required=True, help="path to the input video file."
    )
    parser.add_argument(
        "--input-srt", "-s", required=True, help="path to the input srt file."
    )
    parser.add_argument(
        "--target-language",
        "-l",
        required=True,
        help="target language code (e.g., 'eng', 'spa', 'fra'). supports both iso 639-1 and iso 639-3 formats.",
    )
    # outputs
    parser.add_argument(
        "--output-dir",
        "-o",
        help="output directory. default: data/outputs/[video_name]_[lang]",
    )
    # tts options
    parser.add_argument(
        "--voice-id", "-v", help="specific tts voice id. overrides automatic selection."
    )
    parser.add_argument(
        "--tts-provider",
        "-p",
        choices=["elevenlabs"],
        default="elevenlabs",
        help="tts provider (default: elevenlabs).",
    )
    parser.add_argument(
        "--use-dynamic-gender-detection",
        action="store_true",
        help="detect speaker gender from original audio to match voice.",
    )
    # audio mixing options
    parser.add_argument(
        "--vocals-volume",
        "-vv",
        type=float,
        default=5.0,
        help="dubbed vocals volume adjustment in db (default: 5.0).",
    )
    parser.add_argument(
        "--background-volume",
        "-bv",
        type=float,
        default=-10.0,
        help="original background audio volume adjustment in db (default: -10.0).",
    )
    parser.add_argument(
        "--no-background",
        action="store_true",
        help="skip merging with original background audio.",
    )
    parser.add_argument(
        "--force-vocals-only",
        action="store_true",
        help="force using only the generated vocals, even if background audio exists.",
    )
    parser.add_argument(
        "--skip-background-if-error",
        "-sb",
        action="store_true",
        help="use vocals only if background merge fails.",
    )
    # workflow options
    parser.add_argument(
        "--skip-existing",
        "-se",
        action="store_true",
        help="reuse existing generated utterance audio files.",
    )
    parser.add_argument(
        "--add-subtitles",
        "-subs",
        action="store_true",
        help="embed subtitles from the srt into the final video.",
    )
    parser.add_argument(
        "--keep-temp-files",
        "-k",
        action="store_true",
        help="keep intermediate files (extracted audio/video, vocals).",
    )
    parser.add_argument(
        "--debug", "-d", action="store_true", help="enable debug logging."
    )

    args = parser.parse_args()

    # always enable dynamic gender detection if voice id is not provided
    if not getattr(args, "voice_id", None):
        args.use_dynamic_gender_detection = True

    # setup logging level
    log_level = logging.DEBUG if args.debug else logging.INFO
    logger().setLevel(log_level)
    logger().info(f"log level set to {logging.getLevelName(log_level)}")

    # validate inputs
    if not os.path.isfile(args.input_video):
        parser.error(f"input video not found: {args.input_video}")
    if not os.path.isfile(args.input_srt):
        parser.error(f"input srt not found: {args.input_srt}")

    # setup output directory
    if not args.output_dir:
        outputs_base = os.path.join(project_root, "data", "outputs")
        input_basename = os.path.splitext(os.path.basename(args.input_video))[0]
        args.output_dir = os.path.join(
            outputs_base, f"{input_basename}_{args.target_language}"
        )

    try:
        os.makedirs(args.output_dir, exist_ok=True)
        logger().info(f"output directory: {args.output_dir}")
    except OSError as e:
        parser.error(f"failed to create output directory {args.output_dir}: {e}")

    return args


def cleanup_temporary_files(
    args: argparse.Namespace,
    files_to_clean: Dict[str, Optional[str]],
    utterance_metadata: List[Dict],
):
    """remove temporary files unless --keep-temp-files is set."""
    if args.keep_temp_files:
        logger().info("keeping temporary files as requested.")
        return

    logger().info("cleaning up temporary files...")
    paths_to_remove = set()

    # check if final video file exists before cleanup
    final_video_path = files_to_clean.get("final_video")
    if final_video_path:
        if os.path.exists(final_video_path):
            logger().debug(
                f"final video exists: {final_video_path} ({os.path.getsize(final_video_path)/1024:.1f}kb)"
            )
        else:
            logger().warning(
                f"final video doesn't exist before cleanup! path was: {final_video_path}"
            )

    # add individual utterance audio files
    for utterance in utterance_metadata:
        dubbed_path = utterance.get("dubbed_path")
        # ensure it's within the output dir and exists
        if (
            dubbed_path
            and os.path.exists(dubbed_path)
            and os.path.abspath(args.output_dir) in os.path.abspath(dubbed_path)
        ):
            paths_to_remove.add(dubbed_path)
        # add original segment files if they exist
        start_time_str = str(utterance.get("start", "")).replace(".", "_")
        orig_segment_path = os.path.join(
            args.output_dir,
            f"original_segment_{utterance_metadata.index(utterance)+1:04d}.mp3",
        )
        if os.path.exists(orig_segment_path):
            paths_to_remove.add(orig_segment_path)

    # add extracted video/audio, vocals, intermediate audio/video
    for key, path in files_to_clean.items():
        # never delete final products
        if key == "final_video" or key == "final_audio":
            if path and os.path.exists(path):
                logger().debug(f"preserving final output file: {key}={path}")
            continue

        if (
            path
            and os.path.exists(path)
            and os.path.abspath(args.output_dir) in os.path.abspath(path)
        ):
            # special case: don't delete final_audio_path if it's the same as dubbed_vocals_path and no_background was true
            is_final_audio_same_as_vocals = (
                key == "dubbed_vocals" and path == files_to_clean.get("final_audio")
            )
            if not is_final_audio_same_as_vocals:
                paths_to_remove.add(path)

    # delete subtitle file only if embedding was successful and created a new video file
    srt_path = files_to_clean.get("subtitles")
    final_video_path = files_to_clean.get("final_video")
    video_with_subs_path = files_to_clean.get("video_with_subs")
    if (
        srt_path
        and os.path.exists(srt_path)
        and video_with_subs_path
        and video_with_subs_path == final_video_path
    ):
        paths_to_remove.add(srt_path)
        logger().debug(f"marking subtitle file for deletion: {srt_path}")

    deleted_count = 0
    for f_path in paths_to_remove:
        try:
            if os.path.exists(f_path):
                # don't accidentally delete the final video or audio under any circumstances
                if f_path == files_to_clean.get(
                    "final_video"
                ) or f_path == files_to_clean.get("final_audio"):
                    continue

                os.remove(f_path)
                logger().debug(f"deleted temp file: {f_path}")
                deleted_count += 1
        except OSError as e:
            logger().warning(f"error deleting temp file {f_path}: {e}")

    logger().info(f"cleanup complete. removed {deleted_count} temporary files.")

    # check if final files exist after cleanup
    for key in ["final_video", "final_audio"]:
        path = files_to_clean.get(key)
        if path:
            if os.path.exists(path):
                logger().debug(
                    f"final {key.split('_')[1]} verified after cleanup: {path}"
                )
            else:
                logger().error(
                    f"final {key.split('_')[1]} missing after cleanup: {path}"
                )


def main():
    """main function to orchestrate the dubbing process."""
    args = parse_arguments()
    exit_code = 1  # default to error
    # dictionary to keep track of intermediate file paths for cleanup
    file_paths = {
        "video_only": None,
        "original_audio": None,
        "dubbed_vocals": None,  # created by direct_insert...
        "final_audio": None,  # created by create_dubbed_video (might be merged or copy of vocals)
        "dubbed_video_no_subs": None,  # created by create_dubbed_video
        "subtitles": None,
        "video_with_subs": None,  # created by add_subtitles...
        "final_video": None,  # points to the final video product
    }

    try:
        # step 0: log elevenlabs credits before tts if applicable
        tts_credits_before = None
        tts_credits_after = None
        tts = None
        if args.tts_provider.lower() == "elevenlabs":
            tts = TextToSpeechElevenLabs()
            tts_credits_before = tts.get_account_credits()
            if tts_credits_before:
                logger().info(
                    f"elevenlabs credits before tts: used={tts_credits_before['character_count']} / {tts_credits_before['character_limit']} (remaining={tts_credits_before['remaining_characters']}) tier={tts_credits_before['tier']} status={tts_credits_before['status']}"
                )

        # step 1: parse srt
        utterance_metadata = parse_srt_file(srt_path=args.input_srt)
        if not utterance_metadata:
            logger().error(
                "failed to parse srt file or file contains no valid utterances."
            )
            return 1
        if not any(utt.get("text", "").strip() for utt in utterance_metadata):
            logger().error(
                "srt file parsed, but contains no text content. nothing to dub."
            )
            return 1

        # step 2: extract audio/video
        file_paths["video_only"], file_paths["original_audio"] = (
            extract_audio_background(
                input_video=args.input_video, output_dir=args.output_dir
            )
        )
        if not file_paths["video_only"] or not file_paths["original_audio"]:
            logger().error("failed to extract audio/video from input.")
            return 1

        # check if the extracted audio file is valid
        is_audio_file_valid = (
            os.path.exists(file_paths["original_audio"])
            and os.path.getsize(file_paths["original_audio"]) > 1000
        )
        if not is_audio_file_valid:
            logger().warning(
                "extracted audio file is too small or invalid, treating video as having no background audio"
            )
            auto_no_background = True
        else:
            # step 2b: check if background merge should be skipped automatically
            auto_no_background = False
            if (
                not args.no_background and not args.force_vocals_only
            ):  # only analyze if user hasn't forced options
                auto_no_background = not _has_meaningful_background_audio(
                    file_paths["original_audio"]
                )
                if auto_no_background:
                    logger().info(
                        "automatically detected minimal background audio - skipping merge."
                    )

        should_skip_merge = (
            args.no_background or args.force_vocals_only or auto_no_background
        )

        # step 3: generate speech (tts)
        utterance_metadata = perform_text_to_speech(
            utterance_metadata=utterance_metadata,
            output_dir=args.output_dir,
            target_language=args.target_language,
            original_audio_path=file_paths[
                "original_audio"
            ],  # pass for gender detection
            voice_id=args.voice_id,
            skip_existing=args.skip_existing,
            tts_provider=args.tts_provider,
            use_dynamic_gender=args.use_dynamic_gender_detection,
        )
        # step 3b: log elevenlabs credits after tts and consumption
        if tts:
            tts_credits_after = tts.get_account_credits()
            if tts_credits_after:
                consumed = (
                    (
                        tts_credits_after["character_count"]
                        - tts_credits_before["character_count"]
                    )
                    if (tts_credits_before and tts_credits_after)
                    else None
                )
                logger().info(
                    f"elevenlabs credits after tts: used={tts_credits_after['character_count']} / {tts_credits_after['character_limit']} (remaining={tts_credits_after['remaining_characters']}) tier={tts_credits_after['tier']} status={tts_credits_after['status']}"
                )
                if consumed is not None:
                    logger().info(
                        f"elevenlabs characters consumed in this run: {consumed}"
                    )

        if not any(u.get("for_dubbing") for u in utterance_metadata):
            logger().error(
                "no utterances were successfully processed by tts or marked for dubbing."
            )
            return 1

        # step 4: create dubbed video (combines vocals, optional background, video)
        file_paths["dubbed_video_no_subs"], file_paths["final_audio"] = (
            create_dubbed_video(
                utterance_metadata=utterance_metadata,
                video_only_file=file_paths["video_only"],
                original_audio_file=file_paths["original_audio"],
                output_dir=args.output_dir,
                target_language=args.target_language,
                vocals_volume=args.vocals_volume,
                background_volume=args.background_volume,
                skip_background_if_error=args.skip_background_if_error,
                no_background=should_skip_merge,
                force_vocals_only=args.force_vocals_only,
            )
        )
        # update vocals path if created within create_dubbed_video steps
        potential_vocals_path = os.path.join(args.output_dir, "dubbed_vocals.mp3")
        if os.path.exists(potential_vocals_path):
            file_paths["dubbed_vocals"] = potential_vocals_path

        if not file_paths["dubbed_video_no_subs"] or not file_paths["final_audio"]:
            logger().error("failed to create the dubbed video.")
            return 1

        file_paths["final_video"] = file_paths[
            "dubbed_video_no_subs"
        ]  # initial final video

        # step 5: add subtitles if requested
        if args.add_subtitles:
            file_paths["subtitles"] = save_subtitles(
                utterance_metadata=utterance_metadata,
                output_dir=args.output_dir,
                target_language=args.target_language,
            )
            if file_paths["subtitles"]:
                file_paths["video_with_subs"] = add_subtitles_to_video(
                    video_file=file_paths[
                        "final_video"
                    ],  # add to the current final video
                    subtitles_file=file_paths["subtitles"],
                    language=args.target_language,  # use language code for metadata
                )
                if file_paths["video_with_subs"]:
                    logger().info(
                        f"subtitles embedded successfully into: {os.path.basename(file_paths['video_with_subs'])}"
                    )
                    # update final video path
                    # check if the old final video should be cleaned up now
                    if (
                        file_paths["final_video"] != file_paths["video_with_subs"]
                        and os.path.exists(file_paths["final_video"])
                        and not args.keep_temp_files
                    ):
                        try:
                            os.remove(file_paths["final_video"])
                            logger().debug(
                                f"removed intermediate video without subs: {file_paths['final_video']}"
                            )
                        except OSError as e:
                            logger().warning(
                                f"could not remove intermediate video {file_paths['final_video']}: {e}"
                            )
                    file_paths["final_video"] = file_paths[
                        "video_with_subs"
                    ]  # update final path
                else:
                    logger().warning(
                        f"failed to embed subtitles. srt file available at: {file_paths['subtitles']}"
                    )
            else:
                logger().warning("failed to create subtitles file, skipping embedding.")

        # step 6: final report and cleanup
        logger().info("--------------------")
        logger().info("dubbing process complete.")

        # make sure all paths are absolute
        for key in ["final_video", "final_audio"]:
            if file_paths[key] and os.path.exists(file_paths[key]):
                file_paths[key] = os.path.abspath(file_paths[key])

        # verify final video exists before reporting success
        if file_paths["final_video"] and os.path.exists(file_paths["final_video"]):
            file_size_kb = os.path.getsize(file_paths["final_video"]) / 1024
            logger().info(
                f"final video: {file_paths['final_video']} ({file_size_kb:.1f}kb)"
            )
            exit_code = 0  # success
        else:
            if file_paths["final_video"]:
                logger().error(
                    f"final video file not found at {file_paths['final_video']}"
                )
                # try one last time to copy it from the original video with the audio
                try:
                    logger().warning("attempting emergency recovery of final video...")
                    if (
                        file_paths["video_only"]
                        and file_paths["final_audio"]
                        and os.path.exists(file_paths["video_only"])
                        and os.path.exists(file_paths["final_audio"])
                    ):
                        # use the most basic ffmpeg command possible
                        abs_video = os.path.abspath(file_paths["video_only"])
                        abs_audio = os.path.abspath(file_paths["final_audio"])
                        abs_output = os.path.abspath(file_paths["final_video"])
                        cmd = [
                            "ffmpeg",
                            "-y",
                            "-i",
                            abs_video,
                            "-i",
                            abs_audio,
                            "-c:v",
                            "copy",
                            "-c:a",
                            "aac",
                            abs_output,
                        ]
                        subprocess.run(cmd, check=True, capture_output=True)
                        if os.path.exists(abs_output):
                            logger().info(
                                f"emergency recovery successful! video created at: {abs_output}"
                            )
                            exit_code = 0
                        else:
                            logger().error(
                                "emergency recovery failed - final video still missing"
                            )
                    else:
                        logger().error(
                            "emergency recovery not possible - missing input files"
                        )
                except Exception as e:
                    logger().error(f"emergency recovery failed: {e}")
            else:
                logger().error("final video path not set")
                exit_code = 1

        if file_paths["final_audio"] and os.path.exists(file_paths["final_audio"]):
            file_size_kb = os.path.getsize(file_paths["final_audio"]) / 1024
            logger().info(
                f"final audio: {file_paths['final_audio']} ({file_size_kb:.1f}kb)"
            )
        else:
            logger().warning("final audio file not found or not created")

        if (
            file_paths["subtitles"]
            and os.path.exists(file_paths["subtitles"])
            and not file_paths["video_with_subs"]
        ):
            logger().info(f"subtitles file (not embedded): {file_paths['subtitles']}")
        logger().info("--------------------")

    except Exception as e:
        logger().exception(f"an unexpected error occurred in the main process: {e}")
        exit_code = 1
    finally:
        # make sure we can see the final files before cleanup
        for key in ["final_video", "final_audio"]:
            if file_paths[key]:
                if os.path.exists(file_paths[key]):
                    logger().debug(
                        f"final {key.split('_')[1]} pre-cleanup: {file_paths[key]} ({os.path.getsize(file_paths[key])/1024:.1f}kb)"
                    )
                else:
                    logger().warning(
                        f"final {key.split('_')[1]} missing pre-cleanup: {file_paths[key]}"
                    )

        # step 7: cleanup temp files regardless of success/failure
        cleanup_temporary_files(args, file_paths, utterance_metadata)
        logger().info("dubbing script finished.")
        # ensure all resources registered via atexit are cleaned up
        # atexit handles calling cleanup_resources()

    return exit_code


if __name__ == "__main__":
    script_exit_code = 1  # default to error
    try:
        script_exit_code = main()
    except SystemExit as e:
        # catch sys.exit() calls from argparse or signal handlers
        script_exit_code = e.code if isinstance(e.code, int) else 1
        logger().info(f"script exited with code {script_exit_code}.")
        raise
    except Exception as e:
        logger().exception(f"unhandled exception in __main__: {e}")
        script_exit_code = 1
    finally:
        # final cleanup attempt (atexit should handle this, but as a safeguard)
        cleanup_resources()
        logger().info(f"script final exit code: {script_exit_code}.")
        # use os._exit to bypass potential cleanup issues if threads are stuck
        os._exit(script_exit_code)

# the logging messages are always must be in lowercase
# the code comments except var names or func names always must be in lowercase
# clean from redundant log messages - reviewed and reduced verbosity
