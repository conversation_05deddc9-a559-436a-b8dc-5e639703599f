import os
import re
import subprocess
import sys

from src.utils.logger import logger


class Demucs:

    def build_demucs_command(
        self,
        *,
        audio_file: str,
        output_directory: str,
        device: str = "cuda",
        shifts: int = 10,  # increased from 1 to 10
        overlap: float = 0.25,
        jobs: int = 0,
        split: bool = False,  # disable splitting by default
        segment: int | None = None,
        mp3: bool = False,  # use lossless wav by default
        model: str = "htdemucs_ft",  # new model parameter
    ) -> str:
        """builds the demucs audio separation command with quality optimizations.

        args:
            audio_file: the path to the audio file to process.
            output_directory: the output directory for separated tracks.
            device: the device to use ("cuda" or "cpu").
            shifts: the number of random shifts for equivariant stabilization.
            overlap: the overlap between splits.
            jobs: the number of jobs to run in parallel.
            split: whether to split audio into chunks (disabled for quality).
            segment: the split size for chunks (none for no splitting).
            mp3: convert output to mp3 (not recommended for quality).
            model: the separation model to use (htdemucs_ft for best quality).

        returns:
            a string representing the constructed command.
        """
        command_parts = [
            sys.executable,
            "-m",
            "demucs.separate",
            "-n",
            model,  # specify quality model
            "-o",
            f'"{output_directory}"',
            "--device",
            device,
            "--shifts",
            str(shifts),
            "--overlap",
            str(overlap),
            "-j",
            str(jobs),
            "--two-stems",
            "vocals",
        ]

        if not split:
            command_parts.append("--no-split")
        elif segment is not None:
            command_parts.extend(["--segment", str(segment)])

        command_parts.append(f'"{audio_file}"')
        return " ".join(command_parts)

    def execute_demucs_command(self, command: str) -> None:
        """executes a demucs command using subprocess."""
        try:
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True, check=True
            )
            logger().debug(result.stdout)
        except subprocess.CalledProcessError as error:
            raise Exception(
                f"error in attempt to separate audio: {error}\n{error.stderr}"
            )

    def _extract_command_info(self, command: str) -> tuple[str, str, str]:
        """extracts output directory, file extension, and input filename."""
        folder_pattern = r"-o\s+(['\"]?)(.+?)\1"
        input_file_pattern = r"['\"]?(\w+\.\w+)['\"]?$|\s(\w+\.\w+)$"

        folder_match = re.search(folder_pattern, command)
        input_file_match = re.search(input_file_pattern, command)

        output_directory = folder_match.group(2) if folder_match else ""
        input_file_name_with_ext = (
            (input_file_match.group(1) or input_file_match.group(2))
            if input_file_match
            else ""
        )
        input_file_name_no_ext = (
            os.path.splitext(input_file_name_with_ext)[0] if input_file_match else ""
        )

        # wav is default output format now
        return output_directory, ".wav", input_file_name_no_ext

    def assemble_split_audio_file_paths(self, command: str) -> tuple[str, str]:
        """returns paths to vocals and background audio files."""
        model_name = "htdemucs_ft"  # updated model name
        output_dir, ext, input_name = self._extract_command_info(command)

        vocals_path = os.path.join(output_dir, model_name, input_name, f"vocals{ext}")
        background_path = os.path.join(
            output_dir, model_name, input_name, f"no_vocals{ext}"
        )
        return vocals_path, background_path
