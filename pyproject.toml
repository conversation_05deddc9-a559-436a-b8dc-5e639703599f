[project]
name = "unreleased-dubbing"
version = "0.1.0"
requires-python = "==3.11.11"
dependencies = [
  "aiohappyeyeballs==2.6.1",
  "aiohttp==3.11.16",
  "aiosignal==1.3.2",
  "alembic==1.15.2",
  "annotated-types==0.7.0",
  "antlr4-python3-runtime==4.9.3",
  "anyio==4.9.0",
  "asteroid-filterbanks==0.4.0",
  "attrs==25.3.0",
  "av==14.2.0",
  "azure-cognitiveservices-speech==1.43.0",
  "beautifulsoup4==4.13.4",
  "cachetools==5.5.2",
  "certifi>=2025.4.26",
  "cffi==1.17.1",
  "charset-normalizer>=3.4.2",
  "click==8.1.8",
  "cloudpickle==3.1.1",
  "cmudict==1.0.32",
  "colorama==0.4.6",
  "coloredlogs==15.0.1",
  "colorlog==6.9.0",
  "contourpy==1.3.1",
  "ctranslate2==4.5.0",
  "cycler==0.12.1",
  "decorator==5.2.1",
  "demucs==4.0.1",
  "deprecated==1.2.18",
  "docopt==0.6.2",
  "dora-search==0.1.12",
  "einops==0.8.1",
  "elevenlabs==1.56.0",
  "faster-whisper==1.1.1",
  "filelock>=3.18.0",
  "flatbuffers==25.2.10",
  "fonttools==4.57.0",
  "frozenlist==1.5.0",
  "fsspec>=2025.5.1",
  "google-ai-generativelanguage==0.6.15",
  "google-api-core==2.24.2",
  "google-api-python-client==2.166.0",
  "google-auth==2.38.0",
  "google-auth-httplib2==0.2.0",

  "google-genai==1.9.0",
  "google-generativeai==0.8.4",

  "googleapis-common-protos==1.69.2",
  "greenlet==3.1.1",
  "grpcio==1.71.0",
  "grpcio-status==1.71.0",
  "h11==0.14.0",
  "httpcore==1.0.7",
  "httplib2==0.22.0",
  "httpx==0.28.1",
  "huggingface-hub==0.30.1",
  "humanfriendly==10.0",
  "hyperpyyaml==1.2.2",
  "idna>=3.10",
  "imageio==2.37.0",
  "imageio-ffmpeg==0.6.0",
  "importlib-metadata==8.6.1",
  "importlib-resources==6.5.2",
  "iso639-lang==2.6.0",
  "jinja2==3.1.4",
  "joblib==1.4.2",
  "julius==0.2.7",
  "kiwisolver==1.4.8",
  "lameenc==1.8.1",
  "lightning==2.5.1",
  "lightning-utilities==0.11.8",
  "loguru==0.7.3",
  "mako==1.3.9",
  "markdown-it-py==3.0.0",
  "markupsafe==2.1.5",
  "matplotlib==3.10.1",
  "mdurl==0.1.2",
  "moviepy==2.1.2",
  "mpmath==1.3.0",
  "mss==10.0.0",
  "multidict==6.4.4",
  "networkx==3.3",
  "nltk==3.9.1",
  "numpy==2.2.4",
  "omegaconf==2.3.0",
  "onesecmail-api==0.1.1",
  "onnxruntime==1.21.0",
  "openunmix==1.3.0",
  "optuna==4.2.1",
  "outcome==1.3.0.post0",
  "packaging==24.2",
  "pandas==2.2.3",
  "pillow==10.2.0",
  "primepy==1.3",
  "proglog==0.1.11",
  "propcache==0.3.1",
  "proto-plus==1.26.1",
  "protobuf==5.29.4",
  "psutil==7.0.0",
  "pyannote-audio==3.3.2",
  "pyannote-core==5.0.0",
  "pyannote-database==5.1.3",
  "pyannote-metrics==3.2.1",
  "pyannote-pipeline==3.0.1",
  "pyasn1==0.6.1",
  "pyasn1-modules==0.4.2",
  "pyaudio==0.2.14",
  "pycparser==2.22",
  "pydantic==2.11.2",
  "pydantic-core==2.33.1",
  "pydantic-settings==2.9.1",
  "pydub==0.25.1",
  "pygments==2.19.1",

  "pyparsing==3.2.3",
  "pyphen==0.17.2",
  "pyreadline3==3.5.4",
  "pysocks==1.7.1",
  "python-dateutil==2.9.0.post0",
  "python-dotenv==1.1.0",
  "pytorch-lightning==2.5.1",
  "pytorch-metric-learning==2.8.1",
  "pytz==2025.2",
  "pyyaml==6.0.2",
  "regex==2024.11.6",
  "requests==2.32.3",
  "retrying==1.3.4",
  "rich==14.0.0",
  "rsa==4.9",
  "ruamel-yaml==0.18.10",
  "ruamel-yaml-clib==0.2.12",
  "safetensors==0.5.3",
  "scikit-learn==1.6.1",
  "scipy==1.15.2",
  "screeninfo==0.8.1",
  "selenium==4.31.0",
  "selgym==0.1.6",
  "semver==3.0.4",
  "sentencepiece==0.2.0",
  "setuptools==78.1.0",
  "shellingham==1.5.4",
  "six==1.17.0",
  "sniffio==1.3.1",
  "sortedcontainers==2.4.0",
  "soundfile==0.13.1",
  "soupsieve==2.6",
  "speechbrain==1.0.2",
  "sqlalchemy==2.0.40",
  "srt==3.5.3",
  "submitit==1.5.2",
  "sympy==1.13.1",
  "tabulate==0.9.0",
  "tenacity==9.1.2",
  "tensorboardx==*******",
  "textstat==0.7.5",
  "threadpoolctl==3.6.0",
  "tokenizers==0.21.1",
  "torch==2.5.1+cu124",
  "torch-audiomentations==0.12.0",
  "torch-pitch-shift==1.2.5",
  "torchaudio==2.5.1+cu124",
  "torchmetrics==1.0.3",
  "torchvision==0.20.1+cu124",
  "tqdm==4.67.1",
  "transformers==4.50.3",
  "treetable==0.2.5",
  "trio==0.29.0",
  "trio-websocket==0.12.2",
  "typer==0.15.2",
  "typing-extensions==4.13.2",
  "typing-inspection==0.4.0",
  "tzdata==2025.2",
  "uritemplate==4.1.1",
  "urllib3==2.4.0",
  "websocket-client==1.8.0",
  "websockets>=13.1,<14.0",
  "win32-setctime==1.2.0",
  "wrapt==1.17.2",
  "wsproto==1.2.0",
  "yandex-speechkit==1.5.0",
  "yarl==1.18.3",
  "zipp==3.21.0",
]

[[tool.uv.index]]
url = "https://download.pytorch.org/whl/cu124"
