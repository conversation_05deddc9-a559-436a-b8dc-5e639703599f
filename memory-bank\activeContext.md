# active context

> this file contains the active context for the current task.

## platform

-   windows 10 (version 10.0 26100)
-   python 3.11.11 (strict requirement)
-   cuda support for gpu acceleration
-   ffmpeg required for video/audio processing

## project summary

aizen platform is an advanced ai-powered video dubbing system that automatically translates and synchronizes audio dialogue from videos into different languages. the system uses a complex pipeline involving audio separation, speech recognition, translation, and text-to-speech synthesis.

## current focus

van mode initialization and project analysis. no specific task has been defined yet.

## important references

_no references defined_

## related files

_no related files defined_
