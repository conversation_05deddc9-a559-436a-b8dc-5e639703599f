import logging
import sys
import codecs
from typing import Optional


class CustomFormatter(logging.Formatter):
    """custom formatter with color support for console output"""

    COLORS = {
        "DEBUG": "\033[0;36m",  # cyan
        "INFO": "\033[0;32m",  # green
        "WARNING": "\033[0;33m",  # yellow
        "ERROR": "\033[0;31m",  # red
        "CRITICAL": "\033[0;37;41m",  # white on red
        "RESET": "\033[0m",  # reset
    }

    def format(self, record):
        # always set color and reset attributes - fix for keyerror: 'color'
        record.color = self.COLORS.get(record.levelname, self.COLORS["RESET"])
        record.reset = self.COLORS["RESET"]
        return super().format(record)


# file logging disabled - only console logging is active
# configure root logger for console output only
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] %(levelname)s [%(filename)s:%(lineno)d] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[],  # no file handlers - console only
)


class UnicodeStreamHandler(logging.StreamHandler):
    """stream handler that ensures proper unicode handling."""

    def __init__(self, stream=None):
        if stream is None:
            stream = sys.stdout
        # wrap the stream with a utf-8 writer
        # for windows compatibility with console output
        stream = codecs.getwriter("utf-8")(stream.buffer)
        super().__init__(stream)


class DubbingLogger:
    """enhanced logger for the ai dubbing project"""

    _instance: Optional[logging.Logger] = None

    @classmethod
    def get_logger(cls) -> logging.Logger:
        """returns a singleton logger instance"""
        if cls._instance is None:
            cls._instance = cls._setup_logger()
        return cls._instance

    @staticmethod
    def _setup_logger() -> logging.Logger:
        # use the dubbing_logger name
        logger = logging.getLogger("dubbing_logger")
        logger.setLevel(logging.INFO)

        # disable propagation to root logger (no file logging needed)
        logger.propagate = False

        # prevent duplicate handlers
        if logger.handlers:
            # remove any existing handlers
            for handler in logger.handlers[:]:
                logger.removeHandler(handler)

        try:
            # console handler with color formatting and unicode support
            console_handler = UnicodeStreamHandler()
            console_handler.setFormatter(
                CustomFormatter(
                    "%(color)s%(levelname)s%(reset)s [%(asctime)s] %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S",
                )
            )
            # add console handler
            logger.addHandler(console_handler)
        except Exception as e:
            # fall back to standard handler if unicode handler fails
            fallback_handler = logging.StreamHandler()
            fallback_handler.setFormatter(
                CustomFormatter(
                    "%(color)s%(levelname)s%(reset)s [%(asctime)s] %(message)s",
                    datefmt="%Y-%m-%d %H:%M:%S",
                )
            )
            logger.addHandler(fallback_handler)
            logger.warning(f"using fallback logger handler due to error: {str(e)}")

        return logger


def logger() -> logging.Logger:
    """returns the application logger instance"""
    return DubbingLogger.get_logger()


# convenience methods for logging
def debug(msg: str, *args, **kwargs):
    logger().debug(msg, *args, **kwargs)


def info(msg: str, *args, **kwargs):
    logger().info(msg, *args, **kwargs)


def warning(msg: str, *args, **kwargs):
    logger().warning(msg, *args, **kwargs)


def error(msg: str, *args, **kwargs):
    logger().error(msg, *args, **kwargs)


def critical(msg: str, *args, **kwargs):
    logger().critical(msg, *args, **kwargs)
